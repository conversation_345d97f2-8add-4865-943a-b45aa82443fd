import React, {useState, useEffect} from 'react'
import {useSelector, useDispatch} from 'react-redux'
import Sidebar from "@components/admin/Sidebar";
import ProductCardAdmin from '@components/admin/ProductCardAdmin'
import {getAllProducts} from '@redux/actions/productAction'
import ShopNavbar from '@components/shop/ShopNavbar';
import ShopFooter from "@components/shop/ShopFooter";
import { useNavigate } from "react-router-dom";
import { getLoggedUser } from "@redux/actions/authAction";

const ManageProducts = () => {

  const [allProducts, setAllProducts] = useState([])

  const products = useSelector(state=> state.productReducer.allProducts)
  const loggedUserData = useSelector(state=>state.authReducer.getMe)

  const navigate = useNavigate()

  const dispatch = useDispatch()

  useEffect(()=>{
    dispatch(getLoggedUser())
    dispatch(getAllProducts())
  },[])

  useEffect(()=>{
    if(products){
      if(products.status === 200){
        if(products.data){
          setAllProducts(products.data.data)
        }
      }
    }
  },[products])

  useEffect(()=>{
    if(loggedUserData){
      if(loggedUserData.status === 200){
        if(loggedUserData.data){
          if(loggedUserData.data.data.role !== "admin"){
            navigate('/shop')
          }
        }
      }else if(loggedUserData.status === 401){
        navigate('/shop')
      }
    }
  },[loggedUserData])


  return (
    <div className="admin-page">
      <ShopNavbar/>
    <div className="container">
      <div className="admin-page-wraper">
        <Sidebar/>
        <section className="dashboard-container">
          <h3>MANAGE PRODUCTS</h3>
          <div className="add-wraper">
            <div className='products-container'>
              {
                allProducts.length > 0 ? (
                  allProducts.map((product, index)=>(
                    <ProductCardAdmin key={index} product={product}/>
                  ))
                ):null
              }
            </div>
          </div>
        </section>
      </div>
    </div>
    <ShopFooter/>
  </div>
  )
}

export default ManageProducts