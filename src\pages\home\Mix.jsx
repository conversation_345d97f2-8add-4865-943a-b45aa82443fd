// SuperFoodsLandingEN.jsx
import React, { useState } from "react";
import { Link } from 'react-router-dom';
import Navbar from "@utilities/Navbar";
import Footer from "@utilities/Footer";
import styles from "./Mix.module.scss"

// Example imports (replace with your actual assets)
import HeroImg from "@images/molasses/1.webp";
import MolassesImg from "@images/molasses/6.webp";
import TahiniImg from "@/images/tahini/3.webp";
import ComboImg from "@/images/molasses/9.webp";
import Salad from "@/images/imgs/salad.webp";
import Waffles from "@/images/imgs/waffles.webp";
import Smoothies from "@/images/imgs/smoothies.webp";

function NutritionTable({ data }) {
  const [open, setOpen] = useState(false);

  return (
    <div className="mt-4">
      <button
        onClick={() => setOpen(!open)}
        className="text-sm text-indigo-600 hover:underline"
      >
        {open ? "Hide Nutrition Facts" : "View Nutrition Facts"}
      </button>
      {open && (
        <div className="overflow-x-auto mt-3 border rounded-xl bg-white shadow p-3 max-h-72 overflow-y-auto">
          <table className="w-full text-sm text-left border-collapse">
            <thead className="bg-gray-50 text-gray-700">
              <tr>
                <th className="px-3 py-2 border">Nutrient</th>
                <th className="px-3 py-2 border">per 100g</th>
              </tr>
            </thead>
            <tbody>
              {Object.entries(data).map(([nutrient, value]) => (
                <tr key={nutrient} className="odd:bg-white even:bg-gray-50">
                  <td className="px-3 py-2 border">{nutrient}</td>
                  <td className="px-3 py-2 border">{value}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}

export default function Mix() {
  const molassesNutrition = {
    Energy: "210.9 Kcal",
    Protein: "1.1 g",
    Carbohydrates: "54.5 g",
    "Total Sugars": "50 g",
    Iron: "10.7 mg",
    Calcium: "60–205 mg",
    Magnesium: "242 mg",
    Potassium: "1460 mg",
    "Vitamin B6": "0.67 mg",
    Polyphenols: "497 mg GAE",
  };

  const tahiniNutrition = {
    Energy: "594 Kcal",
    Protein: "17.4 g",
    Fat: "53.7 g",
    Carbohydrates: "21.2 g",
    Fiber: "9.3 g",
    Calcium: "430 mg",
    Magnesium: "95 mg",
    Phosphorus: "740 mg",
    Iron: "9 mg",
    Zinc: "5 mg",
    "Vitamin B1": "1.22 mg",
    Folate: "98 µg",
    Arginine: "2520 mg",
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white text-gray-800">
      {/* NAVBAR */}
      <header className="sticky top-0 z-40 bg-white/70 backdrop-blur-md border-b">
        <nav className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="flex justify-between items-center h-20">
            <div className="flex gap-6">
              <a href="/adam/molasses" className="font-medium text-gray-700 hover:text-indigo-600">
                Adam Molasses
              </a>
              <a href="/mentor/superfoods" className="font-semibold text-indigo-700">
                Mentor Superfoods
              </a>
            </div>
            <a
              href="/shop"
              className="px-4 py-2 rounded-lg bg-indigo-600 text-white hover:bg-indigo-700 text-sm"
            >
              Shop Now
            </a>
          </div>
        </nav>
      </header>

      {/* HERO */}
      <section className="max-w-7xl mx-auto px-6 lg:px-8 py-16">
        <div className="grid lg:grid-cols-2 gap-10 items-center">
          <div className="space-y-6">
            <h1 className="text-5xl font-extrabold text-gray-900">SuperFoods World</h1>
            <p className="text-lg text-gray-600 max-w-2xl">
              Discover the luxury of natural nutrition. From{" "}
              <span className="font-semibold">Adam Molasses</span> to{" "}
              <span className="font-semibold">Mentor Tahini</span>, each crafted to
              bring authentic taste and valuable nutrients to your lifestyle —
              whether you’re an athlete, a parent, or a gourmet enthusiast.
            </p>
            <a
              href="#catalog"
              className="inline-block px-6 py-3 rounded-lg bg-indigo-600 text-white hover:bg-indigo-700 shadow-lg"
            >
              Explore Catalog
            </a>
          </div>
          <div className="relative">
            <img
              src={HeroImg}
              alt="Superfoods hero"
              className="rounded-2xl shadow-2xl object-cover h-96 w-full"
            />
          </div>
        </div>
      </section>

      {/* CATALOG */}
      <section id="catalog" className="max-w-7xl mx-auto px-6 lg:px-8 py-12">
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Molasses */}
          <article className="bg-white rounded-3xl shadow-xl border p-6 flex flex-col">
            <div className="flex justify-between">
              <div>
                <h3 className="text-xl font-bold">Adam Molasses</h3>
                <p className="text-sm text-gray-500 mt-1">Premium natural energy source</p>
              </div>
              <img
                src={MolassesImg}
                alt="Adam Molasses"
                className="w-24 h-24 object-cover rounded-xl"
              />
            </div>
            <div className="mt-5 grow">
              <h4 className="text-sm font-semibold text-gray-700">Nutrition Snapshot</h4>
              <ul className="mt-2 text-sm text-gray-600 list-disc list-inside">
                <li>Natural source of iron & magnesium</li>
                <li>Rich in potassium for daily balance</li>
                <li>No artificial additives</li>
              </ul>
              <h5 className="mt-4 text-sm font-semibold text-gray-700">Usage Ideas</h5>
              <p className="text-sm text-gray-600">
                Mix into smoothies, drizzle over pancakes, or combine with tahini for
                a balanced spread.
              </p>
              <NutritionTable data={molassesNutrition} />
            </div>
            <a
              href="/adam/molasses"
              className="mt-6 inline-block px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 text-sm"
            >
              View Product
            </a>
          </article>

          {/* Tahini */}
          <article className="bg-white rounded-3xl shadow-xl border p-6 flex flex-col">
            <div className="flex justify-between">
              <div>
                <h3 className="text-xl font-bold">Mentor Tahini</h3>
                <p className="text-sm text-gray-500 mt-1">Luxury plant-based fuel</p>
              </div>
              <img
                src={TahiniImg}
                alt="Mentor Tahini"
                className="w-24 h-24 object-cover rounded-xl"
              />
            </div>
            <div className="mt-5 grow">
              <h4 className="text-sm font-semibold text-gray-700">Nutrition Snapshot</h4>
              <ul className="mt-2 text-sm text-gray-600 list-disc list-inside">
                <li>High in plant protein</li>
                <li>Rich in calcium & phosphorus</li>
                <li>Cold-pressed sesame seeds</li>
              </ul>
              <h5 className="mt-4 text-sm font-semibold text-gray-700">Usage Ideas</h5>
              <p className="text-sm text-gray-600">
                Add to smoothies, use as a spread, or stir into dressings for creamy
                texture.
              </p>
              <NutritionTable data={tahiniNutrition} />
            </div>
            <a
              href="/mentor/tahini"
              className="mt-6 inline-block px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 text-sm"
            >
              View Product
            </a>
          </article>

          {/* Combo */}
          <article className="bg-gradient-to-br from-white to-indigo-50 rounded-3xl shadow-2xl border p-6 flex flex-col">
            <div className="flex justify-between">
              <div>
                <h3 className="text-xl font-bold">Molasses + Tahini Combo</h3>
                <p className="text-sm text-gray-500 mt-1">Balance of taste & nutrients</p>
              </div>
              <img
                src={ComboImg}
                alt="Combo"
                className="w-24 h-24 object-cover rounded-xl"
              />
            </div>
            <div className="mt-5 grow">
              <p className="text-sm text-gray-600">
                Together, they create a powerhouse mix: molasses for natural energy
                and minerals, tahini for protein and healthy fats — a traditional
                pairing reimagined for modern wellness.
              </p>
              <p className="mt-3 text-sm text-gray-600">
                Suggested Serving: 2 tbsp molasses + 1 tbsp tahini, stirred into
                breakfast bowls or spread over toast.
              </p>
            </div>
            <div className="mt-6 flex gap-3">
              <a
                href="/adam/molasses"
                className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 text-sm"
              >
                Adam Molasses
              </a>
              <a
                href="/mentor/tahini"
                className="px-4 py-2 border border-emerald-200 text-emerald-700 rounded-lg text-sm hover:bg-emerald-50"
              >
                Mentor Tahini
              </a>
            </div>
          </article>
        </div>
      </section>

      {/* USAGE IDEAS */}
      <section className="max-w-7xl mx-auto px-6 lg:px-8 py-12">
        <div className="bg-white rounded-3xl shadow-xl p-8 border">
          <h3 className="text-2xl font-bold text-gray-900">Smart Usage Examples</h3>
          <p className="text-gray-600 mt-2">
            Quick, creative ways to integrate our superfoods into everyday life.
          </p>
          <div className="mt-6 grid md:grid-cols-3 gap-6">
            <div className="p-4 border rounded-lg">
              <img
                src={Salad}
                alt="Salad"
                className="w-full h-36 object-cover rounded-lg"
              />
              <h4 className="mt-3 font-semibold">Salad Dressings</h4>
              <p className="text-sm text-gray-600">Whisk tahini & molasses with lemon for a rich dressing.</p>
            </div>
            <div className="p-4 border rounded-lg">
              <img
                src={Waffles}
                alt="Waffles"
                className="w-full h-36 object-cover rounded-lg"
              />
              <h4 className="mt-3 font-semibold">Healthy Treats</h4>
              <p className="text-sm text-gray-600">Replace syrups with molasses for a natural topping.</p>
            </div>
            <div className="p-4 border rounded-lg">
              <img
                src={Smoothies}
                alt="Smoothies"
                className="w-full h-36 object-cover rounded-lg"
              />
              <h4 className="mt-3 font-semibold">Smoothie Boost</h4>
              <p className="text-sm text-gray-600">Blend tahini & molasses into your morning shake.</p>
            </div>
          </div>
        </div>
      </section>

      {/* FOOTER */}
      <footer className="max-w-7xl mx-auto px-6 lg:px-8 py-12 text-sm text-gray-500 border-t">
        © {new Date().getFullYear()} Adam & Mentor. All rights reserved.
      </footer>
    </div>
  );
}
