// =============================
// 📦 ملف أدوات الملاءمة (Responsive Helpers)
// يحتوي فقط على المتغيرات والميكسينات الضرورية للستايلنج المتجاوب
// توثيق: محمود - مشروع آدم
// =============================
@use "sass:map";

// --- نقاط التوقف (Breakpoints) ---
$breakpoints: (
  'xs': 0,
  'sm': 576px,
  'md': 768px,
  'lg': 992px,
  'xl': 1200px,
  'xxl': 1400px,
  'xs-down': 575.98px,
  'sm-down': 767.98px,
  'md-down': 991.98px,
  'lg-down': 1199.98px,
  'xl-down': 1399.98px,
  'xs-up': 0,
  'sm-up': 576px,
  'md-up': 768px,
  'lg-up': 992px,
  'xl-up': 1200px
);

// --- متغيرات الألوان ---
$mainColor: #8f6b29; // اللون الأساسي للبراند

// --- متغيرات الصور الدائرية ---
$img-circular-sizes: (
  'lg': 200px,
  'md': 150px,
  'sm': 120px,
  'xs': 100px
);

// --- متغيرات إضافية ---
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;
$paragraphImgWidth: 300px;
$borderRadius: 7px;

// --- دوال مساعدة ---
@function breakpoint($key) {
  @return map.get($breakpoints, $key);
}

// --- ميكسين media ---
/// مكسين موحد لاستعلامات الوسائط
/// مثال: @include media('md')
@mixin media($breakpoint, $orientation: null, $feature: null) {
  $value: breakpoint($breakpoint);
  $media-query: '';
  @if $feature != null {
    $media-query: '(#{$feature}: #{$value})';
  } @else {
    $media-query: '(max-width: #{$value})';
  }
  @if $orientation != null {
    $media-query: $media-query + ' and (orientation: #{$orientation})';
  }
  @media #{$media-query} {
    @content;
  }
}

// --- ميكسين img-circular ---
/// مكسين لتدوير الصور
@mixin img-circular($size) {
  width: $size;
  height: $size;
  border-radius: 50%;
  object-fit: cover;
} 