import React from 'react';
import { Link } from 'react-router-dom'; // Add this
// Import Swiper React components
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Autoplay } from "swiper/modules";

// Import Swiper styles
import "swiper/css";
import "swiper/css/navigation";
import Product1 from '@images/imgs/product-1.webp'
import Product2 from '@images/imgs/product-2.webp'

const OurProducts = () => {
  return (
    <section className="our-products">
      <div className="container">
        <div className="our-products-wraper">
          <h5>OUR PRODUCTS</h5>
          <h2 className="title">Latest Products</h2>
          <div className="products-container">
            <Swiper
              navigation={true}
              autoplay={{
                delay: 5000,
                disableOnInteraction: true,
              }}
              modules={[Navigation, Autoplay]}
              className="mySwiper"
            >
              <SwiperSlide>
                <div className="slide-number">
                    <p>01</p>
                    <p>02</p>
                </div>
                <div className="product-img">
                <Link to="/molasses">
                    <img loading="lazy" src={Product1} alt="product-1"/>
                </Link>
                </div>
                <div className="product-info">
                    <h3>
                    100% Pure<br/>Unsulfured Fancy<br/>Cane Molasses
                    </h3>
                    <small>ADAM'S PRODUCT</small>
                </div>
              </SwiperSlide>

              <SwiperSlide>
                <div className="slide-number">
                    <p>02</p>
                    <p>02</p>
                </div>
                <div className="product-img">
                <Link to="/tahini">
                    <img loading="lazy" src={Product2} alt="product-1"/>
                </Link>
                </div>
                <div className="product-info">
                    <h3>
                    100% Pure<br/>Sesame Tahini
                    </h3>
                    <small>ADAM'S PRODUCT</small>
                </div>
              </SwiperSlide>
            </Swiper>
          </div>
        </div>
      </div>
    </section>
  );
};

export default OurProducts;
