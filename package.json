{"name": "adam-foods", "version": "0.1.0", "private": true, "dependencies": {"@emotion/babel-plugin": "^11.13.5", "@emotion/react": "^11.7.1", "@emotion/styled": "^10.3.0", "@reduxjs/toolkit": "^2.8.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.0-beta.5", "@testing-library/user-event": "^13.5.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.7.0", "axios": "^1.9.0", "prop-types": "^15.8.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-images-uploading": "^3.1.7", "react-rating-stars-component": "^2.2.0", "react-redux": "^9.0.3", "react-router-dom": "^6.18.0", "react-scripts": "^0.0.0", "redux": "^5.0.0", "redux-thunk": "^3.1.0", "sass": "^1.89.2", "serve": "^14.2.5", "swiper": "^11.2.10", "swiper-react": "^1.0.0", "web-vitals": "^2.1.4"}, "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react-swc": "^3.8.0", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.1"}}