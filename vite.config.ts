import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  root: '.',
  plugins: [
    react({
      jsxRuntime: 'automatic',
      jsxImportSource: '@emotion/react',
      babel: {
        plugins: ['@emotion/babel-plugin']
      }
    })
  ],
  resolve: {
    alias: {
      '@': path.resolve(process.cwd(), './src'),
      '@components': path.resolve(process.cwd(), './src/components'),
      '@pages': path.resolve(process.cwd(), './src/pages'),
      '@styles': path.resolve(process.cwd(), './src/css/styles'),
      '@images': path.resolve(process.cwd(), './src/images'),
      '@utilities': path.resolve(process.cwd(), './src/utilities'),
      '@redux': path.resolve(process.cwd(), './src/redux'),
      '@api': path.resolve(process.cwd(), './src/api'),
    },
  },
  css: {
    preprocessorOptions: {
      scss: {
        // لا تضع أي additionalData هنا
      }
    }
  },
  server: {
    port: 5173,
    open: true,
    hmr: {
      overlay: true,
    },
    watch: {
      usePolling: true,
    },
  },
  // ✅ حل مشكلة hoist-non-react-statics مع Vite
  // تم إضافة hoist-non-react-statics لقائمة include عشان Vite يحولها لـ ESM ويمنع خطأ require is not defined
  optimizeDeps: {
    include: ['react', 'react-dom', 'react-router-dom', 'hoist-non-react-statics'],
    exclude: ['@sentry/react'],
  },
});
