var _g;
function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
import * as React from "react";
function SvgDashboard(_ref, svgRef) {
  let {
    title,
    titleId,
    ...props
  } = _ref;
  return /*#__PURE__*/React.createElement("svg", _extends({
    width: "800px",
    height: "800px",
    viewBox: "0 0 48 48",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title === undefined ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, "dashboard-tile-setting-solid") : title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _g || (_g = /*#__PURE__*/React.createElement("g", {
    id: "Layer_2",
    "data-name": "Layer 2"
  }, /*#__PURE__*/React.createElement("g", {
    id: "Layer_1-2",
    "data-name": "Layer 1"
  }, /*#__PURE__*/React.createElement("g", null, /*#__PURE__*/React.createElement("g", {
    id: "invisible_box",
    "data-name": "invisible box"
  }, /*#__PURE__*/React.createElement("rect", {
    width: 48,
    height: 48,
    fill: "#FFF"
  }), /*#__PURE__*/React.createElement("rect", {
    width: 48,
    height: 48,
    fill: "#FFF"
  }), /*#__PURE__*/React.createElement("rect", {
    width: 48,
    height: 48,
    fill: "#FFF"
  })), /*#__PURE__*/React.createElement("path", {
    d: "M40.5,18.5l3.3-.4A7.5,7.5,0,0,0,44,16a7.5,7.5,0,0,0-.2-2.1l-3.3-.4-.9-.2L39,11.9l.5-.8,2-2.7a13.3,13.3,0,0,0-2.9-2.9l-2.7,2-.8.5-1.4-.6-.2-.9-.4-3.3A7.5,7.5,0,0,0,31,3a7.5,7.5,0,0,0-2.1.2l-.4,3.3-.2.9L26.9,8l-.8-.5-2.7-2a13.3,13.3,0,0,0-2.9,2.9l2,2.7.5.8-.6,1.4-.9.2-3.3.4A7.5,7.5,0,0,0,18,16a7.5,7.5,0,0,0,.2,2.1l3.3.4.9.2.6,1.4-.5.8-2,2.7a13.3,13.3,0,0,0,2.9,2.9l2.7-2,.8-.5,1.4.6.2.9.4,3.3A7.5,7.5,0,0,0,31,29a7.5,7.5,0,0,0,2.1-.2l.4-3.3.2-.9,1.4-.6.8.5,2.7,2a13.3,13.3,0,0,0,2.9-2.9l-2-2.7-.5-.8.6-1.4ZM31,20a4,4,0,1,1,4-4A4,4,0,0,1,31,20ZM6,31H18a2,2,0,0,1,2,2V43a2,2,0,0,1-2,2H6a2,2,0,0,1-2-2V33A2,2,0,0,1,6,31Zm0-4H16a1.1,1.1,0,0,0,1-1.2.6.6,0,0,0-.1-.4,16.7,16.7,0,0,1,0-18.8,1.1,1.1,0,0,0-.5-1.5H6A2,2,0,0,0,4,7V25A2,2,0,0,0,6,27Zm32.6,4.2A17,17,0,0,1,31,33a15.9,15.9,0,0,1-5.7-1,1,1,0,0,0-1.3.7V43a2,2,0,0,0,2,2H38a2,2,0,0,0,2-2V32.1a1,1,0,0,0-1-1Z"
  }))))));
}
const ForwardRef = /*#__PURE__*/React.forwardRef(SvgDashboard);
export default __webpack_public_path__ + "static/media/dashboard.6879eaa0f789dc3c5836dccbdd51a82a.svg";
export { ForwardRef as ReactComponent };