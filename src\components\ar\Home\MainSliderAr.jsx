import React from 'react';
import { Link } from 'react-router-dom';

// Import Swiper React components
import { Swiper, SwiperSlide } from "swiper/react";

// Import Swiper styles
import "swiper/css";
import "swiper/css/pagination";
import Jars from "@images/imgs/hh.webp";
import B1 from "@images/imgs/nn.webp";
import B3 from "@images/imgs/tt.webp";

import { Pagination, Autoplay } from "swiper/modules";

const MainSliderAr = () => {
  const swiperRef = useRef(null);
  const introRef = useRef(null);

  const handleUpdateRootClass = (swiperInstance) => {
    if (!swiperInstance) return;
    swiperRef.current = swiperInstance;
    const rootEl = swiperInstance.el;
    const activeSlide = swiperInstance.slides[swiperInstance.activeIndex];
    const isFull = activeSlide && activeSlide.classList.contains('full');
    if (rootEl) {
      if (isFull) rootEl.classList.add('is-full');
      else rootEl.classList.remove('is-full');
    }
    if (introRef.current) {
      if (isFull) {
        introRef.current.classList.add('is-full');
        introRef.current.classList.remove('is-overlap');
      } else {
        introRef.current.classList.remove('is-full');
        introRef.current.classList.add('is-overlap');
      }
    }
  };
  
  return (
    <section className="intro">
      <div className="social-media-links">
        <div className="social">
          <a href="" target="blanck">
            <span>IN</span>
            <p>INSTAGRAM</p>
          </a>
        </div>
        <div className="social">
          <a href="" target="blanck">
            <span>TW</span>
            <p>TWITTER</p>
          </a>
        </div>
        <div className="social">
          <a href="" target="blanck">
            <span>FB</span>
            <p>FACEBOOK</p>
          </a>
        </div>
      </div>

      <Swiper
        pagination={{ clickable: true }}
        autoplay={{ delay: 4500, disableOnInteraction: false }}
        autoHeight={true}
        onSwiper={(swiper) => handleUpdateRootClass(swiper)}
        onSlideChange={(swiper) => handleUpdateRootClass(swiper)}
        modules={[Pagination, Autoplay]}
        className="mySwiper ar"
      >
        <SwiperSlide>
          <div className="container ar">
            <div className="slide-img">
              <img loading="lazy" src={Jars} alt="slide-img" />
            </div>

            <div className="info-wraper ar">
              <h3>أدم للمواد الغذائية</h3>
              <p>
                تخطى المألوف 30 عاماً
                <br />
                <span>بين يديك</span>
                من الإبداع
              </p>
              <div className="work-with">
                <Link to="">اكتشف الأن</Link>
              </div>
            </div>
          </div>
        </SwiperSlide>

        <SwiperSlide>
          <div className="full">
            <div className="slide-img">
              <img loading="lazy" src={B1} alt="slide-img" />
            </div>
          </div>
        </SwiperSlide>

        <SwiperSlide>
          <div className="full">
            <div className="slide-img">
              <img loading="lazy" src={B3} alt="slide-img" />
            </div>
          </div>
        </SwiperSlide>
      </Swiper>
    </section>
  );
};

export default MainSliderAr;