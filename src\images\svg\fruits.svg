var _g;
function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
import * as React from "react";
function SvgFruits(_ref, svgRef) {
  let {
    title,
    titleId,
    ...props
  } = _ref;
  return /*#__PURE__*/React.createElement("svg", _extends({
    fill: "#8f6B29",
    height: "800px",
    width: "800px",
    id: "Layer_1",
    xmlns: "http://www.w3.org/2000/svg",
    xmlnsXlink: "http://www.w3.org/1999/xlink",
    viewBox: "0 0 512 512",
    xmlSpace: "preserve",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _g || (_g = /*#__PURE__*/React.createElement("g", {
    transform: "translate(1)"
  }, /*#__PURE__*/React.createElement("g", null, /*#__PURE__*/React.createElement("g", null, /*#__PURE__*/React.createElement("path", {
    d: "M237.933,297.773c0-5.12-3.413-8.533-8.533-8.533s-8.533,3.413-8.533,8.533s3.413,8.533,8.533,8.533 S237.933,302.893,237.933,297.773z"
  }), /*#__PURE__*/React.createElement("path", {
    d: "M255,272.173c-5.12,0-8.533,3.413-8.533,8.533s3.413,8.533,8.533,8.533s8.533-3.413,8.533-8.533 S260.12,272.173,255,272.173z"
  }), /*#__PURE__*/React.createElement("path", {
    d: "M237.933,323.373c-5.12,0-8.533,3.413-8.533,8.533s3.413,8.533,8.533,8.533s8.533-3.413,8.533-8.533 S243.053,323.373,237.933,323.373z"
  }), /*#__PURE__*/React.createElement("path", {
    d: "M152.6,314.84c-5.12,0-8.533,3.413-8.533,8.533s3.413,8.533,8.533,8.533c5.12,0,8.533-3.413,8.533-8.533 S157.72,314.84,152.6,314.84z"
  }), /*#__PURE__*/React.createElement("path", {
    d: "M169.667,306.306c5.12,0,8.533-3.413,8.533-8.533s-3.413-8.533-8.533-8.533s-8.533,3.413-8.533,8.533 S164.547,306.306,169.667,306.306z"
  }), /*#__PURE__*/React.createElement("path", {
    d: "M127,289.24c-5.12,0-8.533,3.413-8.533,8.533s3.413,8.533,8.533,8.533s8.533-3.413,8.533-8.533S132.12,289.24,127,289.24 z"
  }), /*#__PURE__*/React.createElement("path", {
    d: "M255,297.773c-5.12,0-8.533,3.413-8.533,8.533s3.413,8.533,8.533,8.533s8.533-3.413,8.533-8.533 S260.12,297.773,255,297.773z"
  }), /*#__PURE__*/React.createElement("path", {
    d: "M511,333.613c0-8.533-2.56-17.92-6.827-27.307c-0.853-1.707-2.56-3.413-5.12-4.267c-2.56-0.853-5.12,0-6.827,0.853 c-14.492,8.352-32.04,15.707-52.043,21.956c34.466-50.836,42.242-117.594,22.177-198.596c-5.12-22.187-17.067-38.4-34.133-46.08 c-7.68-3.413-16.213-5.12-23.893-4.267c-4.267-7.68-11.947-23.893-12.8-51.2c0-4.267-3.413-8.533-7.68-5.973 C370.2,17.88,341.187,23,332.653,50.306c-0.853,3.413,0,7.68,3.413,9.387c0,0,30.72,20.48,29.867,43.52 c0,0.853-0.853,29.867,9.387,46.933c0.81,0,26.989,46.917-22.93,115.921c-16.093-26.554-44.998-44.376-78.308-45.069 c4.437-10.779,6.518-22.26,6.518-34.159c0-52.053-41.813-93.867-93.867-93.867s-93.867,41.813-93.867,93.867 c0,15.497,3.915,30.502,10.993,43.913c-38.356,6.542-70.002,38.242-77.694,77.4c-2.892-1.439-5.705-2.903-8.392-4.406 c-1.707-0.853-4.267-1.707-6.827-0.853c-2.56,0.853-4.267,2.56-5.12,4.267C1.56,315.693-1,325.08-1,334.466 c0,31.406,25.746,59.492,68.267,80.262v70.778c0,5.12,3.413,8.533,8.533,8.533h358.4c5.12,0,8.533-3.413,9.387-8.533v-71.32 C485.598,393.346,511,365.213,511,333.613z M391.533,139.906c-6.827-11.093-7.68-31.573-7.68-38.4 c0-25.6-21.333-46.08-32.427-54.613c5.12-9.387,16.213-12.8,23.893-13.653c2.56,36.693,17.067,55.467,17.92,56.32 c2.56,2.56,5.12,4.267,8.533,3.413c5.973-0.853,12.8,0,18.773,3.413c16.213,6.827,22.187,23.893,24.747,34.133 c21.292,86.019,10.278,153.334-33.031,201.952c-15.735,3.737-32.59,6.884-50.374,9.392c2.615-8.653,4.045-17.66,4.045-27.024 c0-11.283-1.968-22.084-5.575-32.084C411.958,217.264,402.598,160.333,391.533,139.906z M342.839,285.631 c0.02,0.064,0.034,0.133,0.055,0.195c0.088,0.194,0.169,0.391,0.255,0.586c3.68,9.01,5.718,18.889,5.718,29.281 c0,0.674-0.01,1.351-0.029,2.031c-0.002,0.066-0.005,0.133-0.007,0.199c-0.163,5.343-0.913,10.84-2.242,16.216 c-0.876,3.419-2.03,6.809-3.492,10.1c-27.679,3.102-57.275,4.734-88.097,4.734c-15.386,0-30.465-0.408-45.155-1.197 c0.051-0.235,0.097-0.468,0.147-0.703c1.341-5.664,2.058-11.48,2.27-17.324c0.024-0.566,0.039-1.115,0.049-1.658 c0.001-0.069,0.002-0.137,0.004-0.206c0.006-0.366,0.019-0.745,0.019-1.099c0-0.113-0.005-0.226-0.005-0.34 c0.001-0.171,0.005-0.343,0.005-0.514c0-15.091-3.592-29.534-9.933-42.44c12.91-26.951,39.184-44.6,69.667-44.6 C304.042,238.893,331.224,258.065,342.839,285.631z M185.88,110.04c42.667,0,76.8,34.133,76.8,76.8 c0,12.8-2.56,24.747-8.533,35.84c-0.366,0.073-0.73,0.157-1.095,0.234c-25.231,5.188-47.252,20.598-61.307,43.149 c-17.063-22.102-41.762-34.854-69.012-36.557c-8.533-12.8-12.8-27.307-13.653-42.667 C109.08,144.173,143.213,110.04,185.88,110.04z M118.467,246.573c1.258,0,2.51,0.037,3.755,0.103 c0.086,0.004,0.172,0.009,0.257,0.013c22.54,1.27,42.766,13.162,56.147,30.679c2.336,3.179,4.481,6.567,6.4,10.165 c5.753,10.068,9.072,21.348,9.977,32.308c0.04,0.504,0.086,1.007,0.116,1.514c0.026,0.424,0.044,0.847,0.063,1.27 c0.047,1.099,0.084,2.2,0.084,3.308c0,6.569-1.022,13.641-3.045,20.443c-4.982-0.359-9.907-0.768-14.779-1.221 c-1.272-2.924-4.074-4.716-7.776-4.716c-3.005,0-5.414,1.182-6.899,3.189c-47.195-5.431-88.416-15.348-120.248-28.789 C47.64,276.44,80.92,246.573,118.467,246.573z M425.667,422.36v54.613H84.333V422.36c34.883,14.211,78.081,23.528,124.778,27.219 c0.086,0.007,0.173,0.013,0.26,0.02c1.885,0.148,3.776,0.287,5.672,0.417c0.523,0.036,1.049,0.069,1.574,0.104 c1.478,0.097,2.958,0.19,4.442,0.276c0.733,0.042,1.467,0.083,2.202,0.122c1.3,0.07,2.601,0.136,3.905,0.198 c0.837,0.04,1.675,0.078,2.514,0.114c1.231,0.053,2.464,0.1,3.698,0.145c0.878,0.032,1.756,0.065,2.636,0.093 c1.244,0.04,2.49,0.073,3.738,0.105c0.857,0.022,1.712,0.046,2.571,0.064c1.376,0.029,2.755,0.05,4.134,0.069 c0.73,0.01,1.458,0.025,2.189,0.033c2.114,0.022,4.232,0.035,6.354,0.035s4.24-0.013,6.354-0.035 c0.729-0.008,1.453-0.022,2.18-0.033c1.385-0.02,2.769-0.04,4.15-0.07c0.85-0.018,1.696-0.042,2.543-0.064 c1.257-0.032,2.514-0.065,3.767-0.105c0.87-0.028,1.738-0.06,2.606-0.092c1.251-0.046,2.5-0.094,3.748-0.147 c0.816-0.035,1.63-0.072,2.444-0.111c1.345-0.063,2.687-0.131,4.027-0.204c0.685-0.037,1.369-0.074,2.052-0.114 c1.569-0.091,3.135-0.189,4.697-0.292c0.431-0.029,0.862-0.056,1.292-0.085C343.864,446.678,389.288,437.181,425.667,422.36z  M255,434.306c-60.242,0-115.453-9.509-157.597-25.065c-6.018-2.301-11.807-4.753-17.336-7.362 c-0.438-0.11-0.862-0.204-1.274-0.284c-38.909-17.808-62.726-41.429-62.726-67.13c0-4.267,0.853-7.68,1.707-11.947 c7.455,3.787,15.555,7.352,24.2,10.698c0.875,0.342,1.758,0.682,2.643,1.019c0.3,0.113,0.596,0.228,0.898,0.341 c40.401,15.27,92.11,25.718,151.1,29.616c0.402,0.082,0.807,0.14,1.212,0.14c4.91,0.223,9.88,0.445,14.877,0.653 c13.814,0.691,27.93,1.053,42.297,1.053c2.861,0,5.722-0.018,8.582-0.045c0.304-0.003,0.608-0.006,0.911-0.009 c29.239-0.301,58.276-1.986,85.227-5.066c24.747-2.56,47.787-6.827,69.12-11.947c0.769-0.385,1.535-0.769,2.226-1.154 c27.373-6.776,51.453-15.289,71.161-25.299c0.853,4.267,1.707,7.68,1.707,11.947C493.933,389.08,386.413,434.306,255,434.306z"
  }), /*#__PURE__*/React.createElement("path", {
    d: "M135.533,186.84c5.12,0,8.533-3.413,8.533-8.533s-3.413-8.533-8.533-8.533S127,173.186,127,178.306 S130.413,186.84,135.533,186.84z"
  }), /*#__PURE__*/React.createElement("path", {
    d: "M161.133,161.24c5.12,0,8.533-3.413,8.533-8.533s-3.413-8.533-8.533-8.533s-8.533,3.413-8.533,8.533 S156.013,161.24,161.133,161.24z"
  }), /*#__PURE__*/React.createElement("path", {
    d: "M152.6,220.973c5.12,0,8.533-3.413,8.533-8.533s-3.413-8.533-8.533-8.533c-5.12,0-8.533,3.413-8.533,8.533 S147.48,220.973,152.6,220.973z"
  }), /*#__PURE__*/React.createElement("path", {
    d: "M161.133,186.84c0,5.12,3.413,8.533,8.533,8.533s8.533-3.413,8.533-8.533s-3.413-8.533-8.533-8.533 S161.133,181.72,161.133,186.84z"
  }))))));
}
const ForwardRef = /*#__PURE__*/React.forwardRef(SvgFruits);
export default __webpack_public_path__ + "static/media/fruits.c9efdbafce6fef64dcc3933cd89301b7.svg";
export { ForwardRef as ReactComponent };