<!DOCTYPE html>
<!-- saved from url=(0022)chrome://new-tab-page/ -->
<html lang="ar" dir="rtl"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>كتالوج منتجات Mentor - سر حياتك المتجددة | آدم سوبرفودز</title>

  <!-- Fonts -->
  <link href="./كتالوج منتجات Mentor - سر حياتك المتجددة _ آدم سوبرفودز_files/css2" rel="stylesheet">

  <style>
    /* متغيرات الألوان والظلال والتدرجات من ملف الهوية البصرية */
    :root{
      --black:#0a0a0a;
      --nearBlack:#121212;
      --dark:#151515;
      --gold:#c9a35a;
      --gold-strong:#feb406;
      --gold-deep:#8f6B29;
      --coffee:#b8945a;
      --coffee-light:#c4a484;
      --white:#ffffff;
      --muted:#d9d7cf;
      --text:#f7f6f1;
      --text-dim:#cfcac0;
      --shadow-strong: 0 20px 40px rgba(0,0,0,.45);
      --shadow-soft: 0 10px 25px rgba(0,0,0,.25);
      --glow-gold: 0 0 35px rgba(201,163,90,.4);
      --glow-gold-strong: 0 0 45px rgba(201,163,90,.6);
      --shine: linear-gradient(90deg, transparent, rgba(255,255,255,.25), transparent);
      --gold-grad: linear-gradient(135deg, #b78f4a 0%, #e2c079 35%, #c19a5d 60%, #8f6B29 100%);
      --gold-grad-hover: linear-gradient(135deg, #c9a35a 0%, #f1d08a 35%, #d1aa6d 60%, #a07c39 100%);
      --page-padding: min(6vw,60px);
      --radius: 14px;
      --radius-lg: 20px;
      --radius-sm: 8px;
      --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      /* متغيرات زر "Bubble" */
      --btn-white: #ffe7ff;
      --btn-bg: #080808;
      --btn-radius: 100px;
    }

    /* تنسيقات عامة للصفحة بالكامل */
    html,body{
      margin:0; padding:0;
      background: var(--black); /* خلفية داكنة فاخرة */
      color: var(--text); /* لون نص فاتح على الخلفية الداكنة */
      font-family: "Noto Kufi Arabic", system-ui, -apple-system, Segoe UI, Roboto, Ubuntu, "Helvetica Neue", Arial, "Apple Color Emoji", "Segoe UI Emoji"; /* الخط المختار */
      line-height:1.7;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      direction: rtl; /* اتجاه النص من اليمين لليسار */
      overflow-x: hidden; /* منع ظهور شريط التمرير الأفقي */
    }

    /* حاوية المحتوى الرئيسية */
    .container{
      width: min(1180px, 100%);
      margin: 0 auto;
      padding-inline: var(--page-padding);
    }

    /* الأقسام العامة */
    .section{
      padding: clamp(40px, 6vw, 90px) 0;
      position: relative;
      isolation: isolate;
    }

    /* تنسيقات العناوين الفرعية */
    .title{
      margin-bottom: clamp(24px,3vw,40px);
      position: relative;
      text-align: center; /* توسيط العناوين */
    }
    .title h2{
      font-weight:800;
      font-size: clamp(26px, 3.4vw, 40px);
      letter-spacing:.4px;
      color: var(--text);
      margin: 0 0 10px;
      position: relative;
      display: inline-block;
    }
    .title h2::after {
      content: "";
      position: absolute;
      left: 0;
      right: 0;
      bottom: -5px;
      height: 1px;
      background: linear-gradient(90deg, var(--gold), transparent);
      opacity: 0.4;
    }
    .title h5{
      color: var(--gold);
      margin:0 0 8px;
      font-weight:700;
      letter-spacing: .6px;
      position: relative;
      display: inline-block;
    }
    .title h5::before {
      content: "";
      position: absolute;
      left: -15px;
      top: 50%;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: var(--gold);
      transform: translateY(-50%);
      opacity: 0.7;
    }
    .lead{
      color: var(--text-dim);
      font-size: clamp(14px, 1.4vw, 16.5px);
      line-height: 1.8;
      max-width: 800px; /* أقصى عرض للفقرات التمهيدية */
      margin: 0 auto;
    }

    /* الغلاف (Header) */
    header.cover{
      min-height: 86vh;
      display:flex;
      align-items:center;
      background:
        radial-gradient(1200px 500px at 70% 10%, rgba(255,214,137,.1), transparent 60%),
        radial-gradient(900px 600px at 15% 80%, rgba(255,214,137,.08), transparent 60%),
        var(--nearBlack); /* خلفية داكنة مع تدرجات */
      position: relative;
      overflow: hidden;
      padding-bottom: 0;
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;
      box-shadow: none;
    }
    header.cover::before{
      content:"";
      position:absolute; inset:0;
      background: url("https://adamsuperfoods.com/static/media/logo-light.4b0e8ebc9a1852861efd.png") center/180px no-repeat;
      opacity:.03;
      filter: grayscale(1);
      pointer-events:none;
      animation: pulse-subtle 8s ease-in-out infinite;
    }
    @keyframes pulse-subtle {
      0%, 100% { opacity: .03; }
      50% { opacity: .05; }
    }
    .cover .wrap{
      display:grid; gap: clamp(18px,3vw,30px);
      grid-template-columns: 1.1fr .9fr;
      align-items: center; /* توسيط العناصر عموديًا */
    }
    .brand-lockup{
      display:flex; align-items:center; gap:16px;
      color: var(--gold);
      text-transform: uppercase;
      letter-spacing:1.4px;
      font-weight:700;
      position: relative;
      z-index: 2;
      justify-content: flex-start; /* محاذاة لليمين في وضع RTL */
    }
    .brand-lockup img{
      width:56px; height:56px; object-fit:contain; 
      filter: drop-shadow(0 6px 12px rgba(0,0,0,.35));
      animation: float 6s ease-in-out infinite;
    }
    @keyframes float {
      0%, 100% { transform: translateY(0); }
      50% { transform: translateY(-5px); }
    }
    .cover h1{
      font-weight:900; margin:6px 0 12px;
      font-size: clamp(28px,4.5vw,56px);
      line-height:1.15;
      background: var(--gold-grad);
      -webkit-background-clip: text; background-clip:text;
      color: transparent;
      text-shadow: 0 0 0 rgba(0,0,0,0.1);
      position: relative;
      z-index: 2;
      text-align: right; /* محاذاة لليمين */
    }
    .cover h1::after {
      content: "";
      position: absolute;
      right: 0; /* تعديل الموضع ليتناسب مع RTL */
      left: auto;
      width: 80%; /* طول الخط تحت العنوان */
      bottom: -8px;
      height: 1px;
      background: linear-gradient(90deg, var(--gold), transparent); /* تعديل اتجاه التدرج */
      opacity: 0.6;
    }
    .tagline{
      font-size: clamp(14px,1.6vw,18px);
      color: var(--text-dim);
      max-width: 62ch;
      position: relative;
      z-index: 2;
      text-align: right; /* محاذاة لليمين */
    }
    .hero-art{
      position:relative;
      border-radius: var(--radius-lg);
      background: linear-gradient(180deg, rgba(255,255,255,.06), rgba(255,255,255,.02));
      border: 1px solid rgba(201,163,90,.18);
      box-shadow: var(--glow-gold), var(--shadow-strong);
      height: clamp(320px, 40vw, 520px);
      overflow:hidden;
      transition: var(--transition-smooth);
    }
    .hero-art:hover {
      box-shadow: var(--glow-gold-strong), var(--shadow-strong);
      border: 1px solid rgba(201,163,90,.25);
      transform: translateY(-5px);
    }
    .hero-art .glow{
      position:absolute; inset:0;
      background:
        radial-gradient(280px 220px at 70% 30%, rgba(254,180,6,.18), transparent 60%),
        radial-gradient(380px 220px at 20% 80%, rgba(254,180,6,.15), transparent 65%);
      filter: blur(6px);
      pointer-events:none;
      animation: pulse-glow 8s ease-in-out infinite;
    }
    @keyframes pulse-glow {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.7; }
    }
    .hero-art .shine{
      position:absolute; inset:0;
      background: linear-gradient(115deg, transparent 0 40%, rgba(255,255,255,.22) 45%, transparent 55% 100%);
      transform: translateX(-120%);
      animation: shine 4.5s ease-in-out 1.2s infinite;
      mix-blend-mode: screen;
    }
    @keyframes shine{
      0%{ transform: translateX(-120%);}
      60%{ transform: translateX(120%);}
      100%{ transform: translateX(120%);}
    }
    .hero-art .main-shot{
      position:absolute; inset:0;
      display:flex; align-items:center; justify-content:center;
    }
    .product-duo{
      width: min(86%, 760px); aspect-ratio: 16/9;
      margin:auto; border-radius: 14px;
      background:
        radial-gradient(300px 200px at 70% 20%, rgba(255,255,255,.08), transparent 60%),
        #0d0d0d url("https://adamsuperfoods.com/static/media/logo-light.4b0e8ebc9a1852861efd.png") center/160px no-repeat;
      border: 1px solid rgba(255,255,255,.06);
      position: relative; overflow:hidden;
      box-shadow: var(--shadow-strong);
    }
    /* تأثير الانعكاس */
    .product-duo::after{
      content:"";
      position:absolute; left:8%; right:8%; bottom:-6%;
      height:18%;
      background: linear-gradient(180deg, rgba(255,255,255,.15), rgba(255,255,255,0));
      filter: blur(10px); opacity:.35; transform: skewY(-3deg);
    }
    .cover .cta-row{
      justify-content: flex-start; /* محاذاة الأزرار لليمين في الرأس */
    }

    /* أزرار الدعوة للإجراء - تصميم Bubble Button */
    .cta-row{
      display:flex; gap:14px; flex-wrap: wrap; margin-top: 20px;
    }
    .btn{
      outline: none;
      cursor: pointer;
      border: 0;
      position: relative;
      border-radius: var(--btn-radius);
      background-color: var(--btn-bg); /* لون خلفية الزر الأساسي (أسود) */
      transition: all 0.2s ease;
      box-shadow:
        inset 0 0.3rem 0.9rem rgba(255, 255, 255, 0.3),
        inset 0 -0.1rem 0.3rem rgba(0, 0, 0, 0.7),
        inset 0 -0.4rem 0.9rem rgba(255, 255, 255, 0.5),
        0 3rem 3rem rgba(0, 0, 0, 0.3),
        0 1rem 1rem -0.6rem rgba(0, 0, 0, 0.8);
      text-decoration: none; /* إزالة خط تحت النص للروابط */
    }
    .btn .wrap {
      font-size: 1.1em; /* حجم نص الزر */
      font-weight: 500;
      color: var(--text); /* لون النص أبيض */
      padding: 16px 30px; /* مسافة داخلية للزر */
      border-radius: inherit;
      position: relative;
      overflow: hidden;
      display: flex; /* لعرض الأيقونة والنص بجانب بعض */
      align-items: center;
      gap: 8px; /* فجوة بين الأيقونة والنص */
    }
    /* إخفاء النص الثاني */
    .btn .wrap p span:nth-child(2) {
      display: none;
    }
    /* إظهار النص الثاني عند التحويم */
    .btn:hover .wrap p span:nth-child(1) {
      display: none;
    }
    .btn:hover .wrap p span:nth-child(2) {
      display: inline-block;
    }
    .btn .wrap p {
      display: flex;
      align-items: center;
      gap: 12px;
      margin: 0;
      transition: all 0.2s ease;
      transform: translateY(2%);
      mask-image: linear-gradient(to bottom, white 40%, transparent);
    }
    .btn .wrap::before,
    .btn .wrap::after {
      content: "";
      position: absolute;
      transition: all 0.3s ease;
    }
    .btn .wrap::before {
      left: -15%;
      right: -15%;
      bottom: 25%;
      top: -100%;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.12); /* تأثير لامع خفيف */
    }
    .btn .wrap::after {
      left: 6%;
      right: 6%;
      top: 12%;
      bottom: 40%;
      border-radius: 22px 22px 0 0;
      box-shadow: inset 0 10px 8px -10px rgba(255, 255, 255, 0.8);
      background: linear-gradient(
        180deg,
        rgba(255, 255, 255, 0.3) 0%,
        rgba(0, 0, 0, 0) 50%,
        rgba(0, 0, 0, 0) 100%
      );
    }
    .btn:hover {
      box-shadow:
        inset 0 0.3rem 0.5rem rgba(255, 255, 255, 0.4),
        inset 0 -0.1rem 0.3rem rgba(0, 0, 0, 0.7),
        inset 0 -0.4rem 0.9rem rgba(255, 255, 255, 0.7),
        0 3rem 3rem rgba(0, 0, 0, 0.3),
        0 1rem 1rem -0.6rem rgba(0, 0, 0, 0.8);
    }
    .btn:hover .wrap::before {
      transform: translateY(-5%);
    }
    .btn:hover .wrap::after {
      opacity: 0.4;
      transform: translateY(5%);
    }
    .btn:hover .wrap p {
      transform: translateY(-4%);
    }
    .btn:active {
      transform: translateY(4px);
      box-shadow:
        inset 0 0.3rem 0.5rem rgba(255, 255, 255, 0.5),
        inset 0 -0.1rem 0.3rem rgba(0, 0, 0, 0.8),
        inset 0 -0.4rem 0.9rem rgba(255, 255, 255, 0.4),
        0 3rem 3rem rgba(0, 0, 0, 0.3),
        0 1rem 1rem -0.6rem rgba(0, 0, 0, 0.8);
    }
    /* زر ذهبي */
    .btn.gold {
        background: var(--gold-grad);
        box-shadow: 
            inset 0 0.3rem 0.9rem rgba(255, 215, 0, 0.3), /* ظل ذهبي فاتح */
            inset 0 -0.1rem 0.3rem rgba(139, 69, 19, 0.7), /* ظل بني داكن */
            inset 0 -0.4rem 0.9rem rgba(255, 215, 0, 0.5), /* ظل ذهبي متوسط */
            0 3rem 3rem rgba(0, 0, 0, 0.3),
            0 1rem 1rem -0.6rem rgba(0, 0, 0, 0.8);
    }
    .btn.gold .wrap {
        color: var(--dark); /* نص داكن على الخلفية الذهبية */
    }
    .btn.gold:hover {
        box-shadow:
            inset 0 0.3rem 0.5rem rgba(255, 215, 0, 0.4),
            inset 0 -0.1rem 0.3rem rgba(139, 69, 19, 0.7),
            inset 0 -0.4rem 0.9rem rgba(255, 215, 0, 0.7),
            0 3rem 3rem rgba(0, 0, 0, 0.3),
            0 1rem 1rem -0.6rem rgba(0, 0, 0, 0.8);
    }
    /* فواصل الأقسام */
    .fold{
      position: relative; overflow: clip;
    }
    .fold::before{
      content:"";
      position:absolute; inset:0 -20vw auto -20vw; height: 72px;
      background: linear-gradient(180deg, rgba(255,255,255,.08), rgba(255,255,255,0));
      transform: skewY(-3deg); opacity:.3;
      animation: pulse-fold 10s ease-in-out infinite;
    }
    @keyframes pulse-fold {
      0%, 100% { opacity: .3; }
      50% { opacity: .2; }
    }

    /* قسم Hook الكبير - مُعدّل للمستهلك */
    .big-hook{
      background:
        radial-gradient(600px 300px at 85% 20%, rgba(254,180,6,.12), transparent 55%),
        radial-gradient(700px 300px at 15% 80%, rgba(254,180,6,.08), transparent 60%),
        var(--dark); /* خلفية داكنة */
      border-top: 1px solid rgba(255,255,255,.06);
      border-bottom: 1px solid rgba(255,255,255,.06);
    }
    .hook-card{
      border: 1px solid rgba(201,163,90,.28);
      background: linear-gradient(180deg, rgba(255,255,255,.05), rgba(255,255,255,.02));
      border-radius: var(--radius);
      padding: clamp(18px,2.8vw,28px);
      box-shadow: var(--shadow-soft);
    }
    .hook-card h2, .hook-card h5 {
        color: var(--text); /* لون العنوان أبيض */
    }
    .hook-card ul {
        text-align: right;
        list-style: none;
        padding: 0;
    }
    .hook-card ul li {
        color: var(--text-dim);
        position: relative;
        padding-right: 25px;
        margin-bottom: 8px;
    }
    .hook-card ul li::before {
        content: '✓';
        position: absolute;
        right: 0;
        color: var(--gold);
        font-size: 1.2em;
        font-weight: bold;
    }

    /* قسم About Us - مُعدّل للمستهلك */
    .about-grid{
      display:grid;
      gap: clamp(18px,3vw,32px);
      grid-template-columns: 1.1fr .9fr;
      align-items: start;
    }
    .about-card, .photo-card{
      border: 1px solid rgba(201,163,90,.25);
      background: linear-gradient(180deg, rgba(255,255,255,.04), rgba(255,255,255,.02));
      border-radius: var(--radius);
      padding: clamp(18px,2.8vw,28px);
      box-shadow: var(--shadow-soft);
    }
    .photo-card{
      min-height: 320px;
      background:
        radial-gradient(240px 180px at 20% 20%, rgba(254,180,6,.14), transparent 60%),
        #0f0f0f url("https://adamsuperfoods.com/static/media/logo-light.4b0e8ebc9a1852861efd.png") center/120px no-repeat;
      position:relative;
    }
    .photo-card .frame{
      position:absolute; inset:10px;
      border:1px solid rgba(201,163,90,.45);
      border-radius: 12px;
      box-shadow: inset 0 0 0 1px rgba(255,255,255,.04), 0 0 0 1px rgba(0,0,0,.3);
    }

    .timeline{ /* أصبح الآن "رحلتك معنا" */
      display:grid; gap:16px; margin-top: 16px;
      text-align: right;
    }
    .milestone{ /* أصبح الآن "خطوات التحول" */
      display:grid; gap:8px;
      border-right: 3px solid var(--gold); /* خط زمني على اليمين */
      padding-right: 12px;
      padding-left: 0; /* إزالة البادنج الأيسر */
    }
    .milestone strong{ color: var(--gold); }
    .milestone div { color: var(--text-dim); }

    .values{ /* أصبح الآن "قيمنا في كل قطرة" */
      display:grid; grid-template-columns: repeat(2,1fr); gap:10px; margin-top: 16px;
      text-align: center;
    }
    .chip{
      border:1px solid rgba(201,163,90,.28);
      background: linear-gradient(180deg, rgba(255,255,255,.05), rgba(255,255,255,.02));
      color: var(--text);
      padding:10px 12px; border-radius: 10px; font-weight:700;
      box-shadow: var(--shadow-soft);
    }

    /* المنتجات والحلول - Product Split */
    .product-split{
      display:grid; gap: clamp(18px,3vw,28px);
      grid-template-columns: 1fr 1fr;
      align-items: stretch; /* لتكون البطاقات بنفس الارتفاع */
    }

    /* تصميم "الكتاب" */
    .product-card.book {
      position: relative;
      border-radius: var(--radius-lg);
      width: 100%; /* يجب أن تأخذ العرض الكامل للعمود في grid */
      height: 450px; /* ارتفاع ثابت لبطاقة الكتاب، زاد قليلا لاستيعاب المحتوى بشكل أفضل */
      background-color: var(--dark); /* لون خلفية الكتاب عندما يكون مفتوحًا */
      box-shadow: var(--shadow-strong);
      transform-style: preserve-3d;
      perspective: 2000px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--text);
      transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1); /* انتقال سلس عند الفتح */
      will-change: transform; /* تحسين الأداء للرسوم المتحركة */
    }
    .product-card.book:hover,
    .product-card.book.active {
        transform: translateY(-8px) scale(1.02); /* رفع وتكبير خفيف عند التحويم/النقر */
        box-shadow: var(--shadow-strong), var(--glow-gold-strong);
    }

    .product-card.book .product-cover.cover {
      top: 0;
      left: 0; /* يجب أن تكون على اليسار في RTL لكي تنفتح بشكل صحيح */
      position: absolute;
      background-color: var(--nearBlack); /* لون غلاف الكتاب */
      width: 100%;
      height: 100%;
      border-radius: var(--radius-lg);
      cursor: pointer;
      transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1); /* انتقال سلس عند فتح الغطاء */
      transform-origin: 100% 50%; /* نقطة الارتكاز لتدوير الغطاء في RTL */
      box-shadow: var(--shadow-strong);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      backface-visibility: hidden; /* لإخفاء الوجه الخلفي للغطاء */
      z-index: 2; /* تأكد من أن الغطاء فوق المحتوى */
      padding: 20px; /* بادنج للغطاء */
      box-sizing: border-box;
      border: 1px solid rgba(201,163,90,.25); /* إطار ذهبي خفيف */
    }
    
    .product-card.book:hover .product-cover.cover,
    .product-card.book.active .product-cover.cover { /* .active للحركة باللمس */
      transform: rotateY(-175deg); /* تدوير الغطاء عند التحويم/النقر لدرجة أكبر */
      box-shadow: 0 0 15px rgba(0,0,0,.5); /* ظل أضعف للغطاء المفتوح */
      border-color: rgba(201,163,90,.1); /* إطار أضعف عند الفتح */
    }

    .product-card.book .shot {
      width: 80%;
      height: 60%; /* تقليل ارتفاع الصورة قليلاً لتناسب العنوان والرسالة */
      background-size: contain; /* تأكد من ظهور المنتج كاملاً */
      background-repeat: no-repeat;
      background-position: center;
      border-radius: var(--radius);
      border: 1px solid rgba(201,163,90,.1);
      box-shadow: var(--shadow-soft);
      margin-bottom: 10px;
      transition: all 0.5s ease;
    }
    .product-card.book .product-cover.cover:hover .shot {
        transform: scale(1.05); /* تكبير الصورة عند التحويم على الغطاء */
    }

    .product-card.book .product-cover.cover h3 {
        color: var(--gold-strong);
        font-size: 1.8em;
        margin: 10px 0 0;
        text-shadow: 0 2px 5px rgba(0,0,0,0.4);
        transition: all 0.5s ease;
    }
    .product-card.book .product-cover.cover span {
        color: var(--text-dim);
        font-size:0.9em;
        transition: all 0.5s ease;
        opacity: 0.8;
    }
    .product-card.book .product-cover.cover:hover h3,
    .product-card.book .product-cover.cover:hover span {
        text-shadow: 0 0 15px rgba(254,180,6,.8); /* توهج نصي عند التحويم */
        color: var(--text);
    }


    .product-card.book .product-page {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      padding: clamp(16px,2.2vw,22px);
      box-sizing: border-box;
      background-color: var(--dark); /* لون خلفية الصفحة الداخلية */
      border-radius: var(--radius-lg);
      backface-visibility: hidden; /* إخفاء الوجه الخلفي للصفحة الداخلية */
      transform: rotateY(0deg); /* تظهر الصفحة الداخلية عند تدوير الغطاء */
      z-index: 1;
      overflow-y: auto; /* للسماح بالتمرير إذا كان المحتوى طويلاً */
      text-align: right; /* محاذاة لليمين */
      border: 1px solid rgba(201,163,90,.1); /* إطار خفيف للصفحة الداخلية */
      box-shadow: inset 0 0 10px rgba(0,0,0,.5); /* ظل داخلي للصفحة */
    }
    
    .product-card.book .product-page .p-body {
        padding: 0; /* إزالة البادنج الزائد */
    }
    
    .product-card.book .product-page .p-body h3 {
        margin-top: 0;
        color: var(--gold); /* عنوان ذهبي لصفحة المنتج */
        font-size: clamp(1.2em, 2vw, 1.6em); /* حجم الخط متجاوب */
    }
    .product-card.book .product-page p,
    .product-card.book .product-page .lead {
        font-size: clamp(0.9em, 1.2vw, 1.1em); /* حجم خط متجاوب */
        line-height: 1.6; /* زيادة تباعد الأسطر لراحة القراءة */
    }
    
    .product-card.book .product-page .bullets li::before {
        right: 0;
        left: auto;
    }
    
    .product-card.book .product-page .compare strong {
        text-align: right;
    }
    .product-card.book .product-page .compare span {
        text-align: right;
        display: block;
    }

    /* قوة الثنائي */
    .duo{
      margin-top: clamp(10px, 2vw, 18px);
      border: 1px solid rgba(201,163,90,.25);
      background: linear-gradient(180deg, rgba(255,255,255,.03), rgba(255,255,255,.01));
      border-radius: var(--radius);
      padding: clamp(14px,2vw,18px);
      color: var(--text);
      text-align: right;
      box-shadow: var(--shadow-soft);
      transition: var(--transition-smooth);
    }
    .duo:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-strong), var(--glow-gold);
        border-color: rgba(201,163,90,.35);
    }
    .duo h3 {
        color: var(--gold);
        font-size: clamp(1.4em, 2.5vw, 1.8em);
    }
    .duo .lead {
        color: var(--text-dim);
        text-align: right;
        font-size: clamp(0.95em, 1.5vw, 1.15em);
    }
    .duo .bullets li::before {
        right: 0;
        left: auto;
    }

    /* لماذا آدم سوبر فودز - مُعدّل للمستهلك */
    .why-grid{
      display:grid; gap: clamp(18px,3vw,28px);
      grid-template-columns: 1fr 1fr;
    }
    .why-card {
      border-radius: var(--radius-lg);
      background: linear-gradient(180deg, rgba(255,255,255,.04), rgba(255,255,255,.01));
      border: 1px solid rgba(201,163,90,.15);
      padding: 25px;
      transition: var(--transition-smooth);
      box-shadow: var(--shadow-soft);
    }
    .why-card:hover {
      box-shadow: var(--shadow-strong), var(--glow-gold);
      border-color: rgba(201,163,90,.25);
    }
    .icon-grid{
      display:grid; gap:10px;
      grid-template-columns: 1fr; /* لجعل كل عنصر في سطر منفصل */
    }
    .icon-item {
      display: flex;
      gap: 15px;
      margin-bottom: 20px;
      border:1px solid rgba(201,163,90,.25);
      background: linear-gradient(180deg, rgba(255,255,255,.04), rgba(255,255,255,.02));
      padding: 10px 12px; border-radius: 12px;
      box-shadow: var(--shadow-soft);
      text-align: right;
      transition: var(--transition-smooth);
    }
    .icon-item:hover {
        transform: translateX(-5px); /* حركة خفيفة عند التحويم */
        box-shadow: var(--shadow-strong);
        border-color: rgba(201,163,90,.35);
    }
    .icon-item .dot {
      width: 12px;
      height: 12px;
      background: var(--gold);
      border-radius: 50%;
      margin-top: 8px;
      position: relative;
      flex: 0 0 12px;
      box-shadow: 0 0 8px rgba(201,163,90,.7);
      animation: dot-pulse 2s infinite alternate; /* إضافة نبض خفيف للنقطة */
    }
    @keyframes dot-pulse {
        0% { transform: scale(1); opacity: 1; }
        100% { transform: scale(1.1); opacity: 0.8; }
    }
    .icon-item .dot::after {
      content: "";
      position: absolute;
      inset: -4px;
      border-radius: 50%;
      border: 1px solid var(--gold);
      opacity: 0.4;
    }
    .icon-item strong {
        color: var(--gold);
    }
    .icon-item .lead {
        font-size: 0.95em;
        color: var(--text-dim);
        margin-top: 5px;
        text-align: right;
    }

    /* شهادات العملاء */
    .testimonials-section {
        background:
            radial-gradient(600px 300px at 85% 20%, rgba(254,180,6,.12), transparent 55%),
            radial-gradient(700px 300px at 15% 80%, rgba(254,180,6,.08), transparent 60%),
            var(--dark); /* خلفية داكنة */
        border-top: 1px solid rgba(255,255,255,.06);
        border-bottom: 1px solid rgba(255,255,255,.06);
    }
    .customer-testimonials{
      display:grid; gap: clamp(14px, 1.8vw, 18px);
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); /* متجاوب */
    }
    .testimonial-card{
      border:1px solid rgba(201,163,90,.25);
      background: linear-gradient(180deg, rgba(255,255,255,.04), rgba(255,255,255,.02));
      border-radius: var(--radius);
      padding: clamp(14px,2vw,18px);
      box-shadow: var(--shadow-soft);
      text-align: right;
      transition: var(--transition-smooth);
    }
    .testimonial-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-strong), var(--glow-gold);
        border-color: rgba(201,163,90,.25);
    }
    .testimonial-card p{ color: var(--text-dim); font-style: italic; margin-bottom: 10px; }
    .testimonial-card strong{ color: var(--gold); display: block; margin-top: 10px;}
    .testimonial-card span{ color: var(--text-dim); font-size: 0.9em; }

    /* التواصل */
    footer.section#contact{
        background: var(--nearBlack); /* خلفية داكنة */
        border-top: 1px solid rgba(255,255,255,.06);
        color: var(--text);
        text-align: center; /* توسيط المحتوى */
    }
    .contact-info {
        max-width: 800px;
        margin: 0 auto;
    }
    .contact-info p {
        color: var(--text-dim);
    }
    .contact-info a {
        transition: var(--transition-smooth);
        position: relative;
        color: var(--gold);
        text-decoration: none;
    }
    .contact-info a::after {
        content: "";
        position: absolute;
        right: 0;
        left: auto;
        bottom: -2px;
        height: 1px;
        background: var(--gold);
        transform: scaleX(0);
        transform-origin: right;
        transition: var(--transition-smooth);
        opacity: 0.7;
    }
    .contact-info a:hover {
        color: var(--gold-strong) !important;
    }
    .contact-info a:hover::after {
        transform: scaleX(1);
        transform-origin: right;
    }
    .sep{
      height:1px; background: linear-gradient(90deg, transparent, rgba(201,163,90,.4), transparent);
      margin: 16px 0;
    }
    .social-links {
        margin-top: 20px;
        display: flex;
        justify-content: center;
        gap: 20px;
    }
    .social-link {
        color: var(--gold);
        font-size: 2em;
        text-decoration: none;
        transition: transform 0.3s ease;
    }
    .social-link:hover {
        transform: translateY(-3px) scale(1.1);
        color: var(--gold-strong);
    }

    /* Responsive */
    @media (max-width: 980px){
      .cover .wrap{ grid-template-columns: 1fr; } /* عمود واحد في الجوال */
      .hero-art { order: -1; margin-bottom: 30px;} /* الصورة أولاً في الجوال */
      .cover h1, .tagline, .cta-row { text-align: center; } /* توسيط النصوص في الرأس */
      .cover h1::after { left: 50%; right: auto; transform: translateX(-50%); } /* توسيط الخط تحت العنوان */
      .brand-lockup { justify-content: center; }
      
      .about-grid, .product-split, .why-grid{ grid-template-columns: 1fr; }
      .customer-testimonials{ grid-template-columns: 1fr; } /* عمود واحد في الجوال */
      .product-card.book {
          height: 520px; /* زيادة الارتفاع ليتسع المحتوى في الجوال */
      }
      .product-card.book .product-page {
          padding: 15px; /* تقليل البادنج في الجوال */
      }
      .product-card.book .shot {
          width: 90%;
          height: 60%;
      }
      .product-card.book .product-cover.cover h3 {
          font-size: 1.5em;
      }
      .milestone { /* تعديل ليتناسب مع العرض الأصغر */
        border-right: none;
        border-left: 3px solid var(--gold);
        padding-left: 12px;
        padding-right: 0;
        text-align: left;
      }
      .milestone strong, .milestone div { text-align: left; }
    }
    @media (max-width: 620px){
      .values{ grid-template-columns: 1fr; }
      .cta-row { flex-direction: column; }
      .btn { width: 100%; }
      .product-card.book {
          height: 580px; /* زيادة الارتفاع أكثر في الشاشات الصغيرة جدًا */
      }
      .product-card.book .product-cover.cover h3 {
          font-size: 1.4em;
      }
    }
    @media (min-width: 981px) and (orientation: landscape) {
        /* تحسينات للي اوت الأفقي على الشاشات الكبيرة */
        .product-card.book {
            height: 400px; /* ممكن تظبطها هنا تاني عشان الشاشات الكبيرة */
        }
    }
    @media (max-width: 980px) and (orientation: landscape) {
        /* تحسينات للي اوت الأفقي على التابلت والموبايل */
        .cover .wrap {
            grid-template-columns: 1fr 1fr; /* ممكن نرجع عمودين لو المساحة الأفقية تسمح */
            gap: 20px;
        }
        .hero-art {
            order: initial; /* نرجع ترتيب الصورة عادي */
            margin-bottom: 0;
        }
        .cover h1, .tagline, .cta-row, .brand-lockup {
            text-align: right; /* نرجع المحاذاة لليمين */
            justify-content: flex-start;
        }
        .cover h1::after {
            left: auto;
            right: 0;
            transform: translateX(0);
        }
        .product-split, .why-grid {
            grid-template-columns: 1fr 1fr; /* ممكن نرجع عمودين في المنتجات */
        }
        .product-card.book {
            height: 400px; /* نرجع ارتفاع الكتاب لأصغر شوية عشان المساحة الأفقية */
        }
        .product-card.book .product-page {
            font-size: 0.9em; /* تصغير الخط قليلاً لو المساحة ضيقة */
            line-height: 1.5;
        }
        .milestone { /* تعديل ليتناسب مع العرض الأفقي */
            border-right: 3px solid var(--gold);
            border-left: none;
            padding-right: 12px;
            padding-left: 0;
            text-align: right;
        }
        .milestone strong, .milestone div { text-align: right; }
    }


  </style>
</head>
<body>

  <!-- 1) الغلاف Cover - الجزء العلوي من الكاتالوج اللي بيخطف العين -->
  <header class="cover section">
    <div class="container">
      <div class="wrap">
        <div>
          <div class="brand-lockup">
            <img src="./كتالوج منتجات Mentor - سر حياتك المتجددة _ آدم سوبرفودز_files/logo-light.4b0e8ebc9a1852861efd.png" alt="Adam Superfoods Logo">
            <span>آدم سوبرفودز</span>
          </div>
          <h1>ارتقِ بحياتك: <span style="background: var(--gold-grad); -webkit-background-clip: text; background-clip:text; color: transparent;">قوتك الحقيقية تبدأ من جواك</span></h1>
          <p class="tagline">
            هل زهقت من الحلول اللي على قدها والوعود اللي بتطير في الهوا؟ منتجات Mentor من آدم سوبرفودز هي مفتاحك لطاقة مالهاش آخر، وصحة متكاملة، وحياة كلها حيوية ونشاط.
          </p>
          <div class="cta-row">
            <a href="chrome://new-tab-page/#products" class="btn gold">
                <div class="wrap">
                    <p>
                        <span>طريقك للتحول لنسختك الخارقة <span style="font-size:90%">⟶</span></span>
                        <span>أطلق العنان لقواك الكامنة بداخلك <span style="font-size:90%">✨</span></span>
                    </p>
                </div>
            </a>
            <a href="chrome://new-tab-page/#why" class="btn">
                <div class="wrap">
                    <p>
                        <span>ليه Mentor هو اختيارك الأذكى؟</span>
                        <span>اعرف ليه<span style="font-size:90%">🔬</span></span>
                    </p>
                </div>
            </a>
          </div>
        </div>

        <div class="hero-art">
          <span class="glow"></span>
          <span class="shine"></span>
          <div class="main-shot">
            <!-- عرض بصري فاخر للثنائي (دبس القصب + طحينة منتور) -->
            <div class="product-duo" aria-label="عرض بصري فاخر للثنائي (دبس القصب + طحينة منتور)" style="background-image: url(&#39;https://previews.dropbox.com/p/thumb/ACtbIGCN9mlUB15MLomkWQJJitYf5mhJj4GP2qPxE-symGHs8lsh1rMJx5QrwagK8uX2NsRwqKKClNB8hHL4oYWTWoOJ_DABn5iomu9mAUPmpDFj0nv5HZAwziQBj-FZ5_uo2f3_lbs-xESK-5eeXVjfgj6bKZP-t-MYMLlimjbfui_5BvNRnT7Ep5H3NKg6gzfQ0UaAqYwin9rrXURVbgJAeuwnWrPs3maqHVBQhmINoUOQ-st1mrLkDJ27j0KGfktiK8Dvq3WKAmf0-VY0osL8dLqeW7B0kxFKoN5OUFPXAwKgjobPfRyUa2GMszFV6OzwEROP7w1CCeXgKMazZb8i/p.png?is_prewarmed=true;); background-size: contain; background-repeat: no-repeat; background-position: center;"></div>
          </div>
        </div>
      </div>

      <div class="sep" style="margin-top:28px;"></div>

      <!-- Catchphrase - جملة قوية عشان تجذب الانتباه -->
      <p class="lead" style="text-align:center;"><strong style="color:var(--gold)">
        مش مجرد أكل صحي: </strong>منتجات Mentor هي استثمارك في صحة تدوم، طاقة بتتجدد، وحياة تفوق كل توقعاتك. <strong>انضم للنخبة اللي بتختار القيمة، مش مجرد السعر.</strong>
      </p>
    </div>
  </header>

  <!-- 2) The Big Hook - تحدي المشاكل اليومية -->
  <section class="big-hook section fold" id="hook">
    <div class="container">
      <div class="hook-card">
        <div class="title">
          <h5>المعاناة اليومية بتخلص هنا</h5>
        <h2>حاسس بتعب وإرهاق على طول؟ نظامك الغذائي مش جايب نتيجة؟</h2>
      </div>
      <p class="lead">
        في عالم كله ضغوط واختيارات أكل محيرة، إنك تحافظ على صحتك وطاقتك بقى تحدي كبير. كتير مننا بيدور على حلول سريعة أو مكملات صناعية بتوعد بس من غير ما تنفذ، وتسبنا في دايرة مقفولة من الإحباط والنتائج المحدودة.
      </p>
      <p class="lead">
        <strong>طب إيه رأيك لو فيه طريق تاني؟</strong> طريق طبيعي، مبني على سنين طويلة من البحث، يدي جسمك كل اللي محتاجه عشان يبقى في أحسن حالاته من جوه، ويحول أكلك لمصدر قوة حقيقية؟
      </p>
      <div style="background:rgba(201,163,90,.08);border:1px dashed rgba(201,163,90,.35);border-radius:12px;padding:16px;margin-top:16px;text-align:right;">
        <strong style="color:var(--gold);display:block;margin-bottom:8px;">إشارات لازم تاخد بالك منها:</strong>
        <ul style="margin:0;padding-right:20px;">
          <li>دايماً حاسس بإرهاق وتعب وقلة تركيز.</li>
          <li>صعب عليك تخس أو تبني عضلات.</li>
          <li>بتدور على بدائل صحية للسكر من غير ما تضحي بالطعم الحلو.</li>
          <li>نفسك تقوي مناعتك وصحتك العامة بطرق طبيعية.</li>
          <li>قلقان على نقاء وجودة الأكل اللي بتاكله.</li>
        </ul>
      </div>
    </div>
  </div>
  </section>

  <!-- 3) About Us - حكاية النقاء اللي ورانا -->
<section class="section" id="about">
  <div class="container">
    <div class="title">
      <h5>حكايتنا — سنين طويلة من البحث عن النقاء المطلق</h5>
      <h2>آدم سوبرفودز: <span style="background: var(--gold-grad); -webkit-background-clip: text; background-clip:text; color: transparent;">30 سنة تفاني في صحتك</span></h2>
    </div>

    <div class="about-grid">
      <div class="about-card">
        <p style="text-align:right;">
          على مدار <strong style="color:var(--gold);">تلاتين سنة</strong>، ماكناش مجرد مصنعين، لأ، كنا باحثين ومطورين لمنتجات أكل فائقة الجودة. رحلتنا بدأت من أرض مصر، واكتشفنا أسرار الطبيعة وقدرتها على تغيير الصحة. قضينا سنين طويلة بندرس المواد الخام، وأدق خواص كل مكون لحد المستوى الذري، عشان نوفرلك دلوقتي منتجات "Mentor" — اللي مش بس نقية وطبيعية، دي كمان <strong style="color:var(--gold);">أحسن من معظم المكملات الصيدلانية وأكل الأطفال والرياضيين كمان.</strong>
        </p>

        <div class="timeline">
          <div class="milestone">
            <strong>البداية (1995)</strong>
            <div>شغفنا بالنقاء خلانا نطور دبس القصب المصري بمعايير تفوق كل التوقعات — <span style="color:var(--gold);">ودينا لجيل كامل طاقة طبيعية مفيش زيها قبل كده</span>.</div>
          </div>
          <div class="milestone">
            <strong>الاكتشاف (2008)</strong>
            <div>سنين البحث الكتير خلتنا نفهم السمسم بشكل أعمق ونعرف قدراته — <span style="color:var(--gold);">عملنا ثورة في عالم الطحينة بطعم وقيمة لا يعلى عليها</span>.</div>
          </div>
          <div class="milestone">
            <strong>الابتكار (2016)</strong>
            <div>استخدمنا تقنيات معالجة فيزيائية حصرية ومسجلة ببراءة اختراع — <span style="color:var(--gold);">عشان نحافظ على 98% من القيمة الغذائية الأصلية</span>.</div>
          </div>
          <div class="milestone">
            <strong>إرث النهاردة</strong>
            <div>منتجات "Mentor" — خلاصة 30 سنة خبرة عشان تديلك <span style="color:var(--gold);">التحول الصحي اللي تستحقيه</span>.</div>
          </div>
        </div>

        <div class="values">
          <div class="chip">نقاء مطلق</div>
          <div class="chip">علم موثوق</div>
          <div class="chip">قيمة تستاهل أكتر من تمنها</div>
          <div class="chip">تغيير يدوم</div>
        </div>
      </div>

      <!-- صورة/إطار — هنا ممكن نحط صورة تعبر عن الطبيعة/الجودة/المنتجات بتاعتنا -->
      <div class="photo-card" aria-label="صورة تعبّر عن جودة منتجات آدم سوبرفودز وطبيعتها" style="background-image: url(&#39;https://i.imgur.com/gK2J2pG.jpg&#39;); background-size: cover; background-position: center;">
        <div class="frame"></div>
      </div>
    </div>
  </div>
</section>

  <!-- 4) Products & Solutions - تفاصيل المنتجات وقيمتها اللي هترجع عليك -->
  <section class="section" id="products">
  <div class="container">
    <div class="title">
      <h5>الحلول الطبيعية لحياة أحسن</h5>
      <h2>منتجات Mentor: <span style="background: var(--gold-grad); -webkit-background-clip: text; background-clip:text; color: transparent;">قوة الطبيعة بين إيديك</span></h2>
      <p class="lead">كل نقطة وكل معلقة من منتجات Mentor هي وعد بتغيير حقيقي لصحتك. نقية، فاخرة، ومصممة عشان تديلك أحسن ما في الطبيعة.</p>
    </div>

    <!-- دبس القصب -->
    <div class="product-split">
      <div class="product-card book" tabindex="0"> <!-- أضف tabindex للسماح بالتركيز على اللمس/لوحة المفاتيح -->
        <div class="product-cover cover">
          <div class="shot" style="background-image: url(&#39;https://i.imgur.com/G4iO2R0.jpg&#39;);"></div>
          <h3>دبس منتور — طاقة متخلصش</h3>
          <span style="color:var(--text-dim); font-size:0.9em;">دوس أو مرر عشان تعرف أكتر ⟶</span>
        </div>
        <div class="product-page">
          <div class="p-body">
            <h3>دبس منتور — محلي طبيعي، طاقة متخلصش</h3>
            <p>خلاص كفاية سكر مكرر ووعود مكملات كدابة! دبس منتور مش مجرد حاجة بتحلي الأكل، ده **مصفوفة غذائية خرافية** مليانة حديد، مغنيسيوم، بوتاسيوم، ومجموعة فيتامينات B كاملة، ومضادات أكسدة. ده سر الطاقة اللي بتفضل معاك، والنقاء اللي ملوش مثيل.</p>
            <ul class="bullets">
              <li><strong>طاقة ذكية ومستقرة:</strong> بيديلك حيوية ونشاط يفضلوا معاك اليوم كله، من غير أي ارتفاع مفاجئ للسكر أو طاقة تفصل منك فجأة. <strong style="color:var(--gold);">وداعاً لتعب نص اليوم.</strong></li>
              <li><strong>بديل سكر أمان وصحي:</strong> استمتع بالحلاوة الطبيعية مع قيمة غذائية مفيش زيها. <strong style="color:var(--gold);">مناسب لكل الناس، حتى مرضى السكر.</strong></li>
              <li><strong>دم صحي وعظام قوية:</strong> بيقوي إنتاج كرات الدم الحمرا والعضم، وبيحميك من التعب وهشاشة العظام. <strong style="color:var(--gold);">قوة من جواك هتحسها وهتظهر عليك.</strong></li>
            </ul>
            <div class="compare">
              <strong>ليه أحسن من أي حاجة تانية؟</strong>
              <span>أحسن من السكر والستيفيا عشان قيمته الغذائية عالية جداً، وأحسن من مكملات الحديد عشان جسمك بيمتصه كله طبيعي. <strong style="color:var(--gold);">دي الحلاوة اللي بتبني صحتك.</strong></span>
            </div>
          </div>
        </div>
      </div>

      <!-- طحينة منتور -->
      <div class="product-card book" tabindex="0"> <!-- أضف tabindex للسماح بالتركيز على اللمس/لوحة المفاتيح -->
        <div class="product-cover cover">
          <div class="shot" style="background-image: url(&#39;https://i.imgur.com/Y1719B5.webp&#39;);"></div>
          <h3>طحينة منتور — سر القوة والجمال</h3>
          <span style="color:var(--text-dim); font-size:0.9em;">دوس أو مرر عشان تعرف أكتر ⟶</span>
        </div>
        <div class="product-page">
          <div class="p-body">
            <h3>طحينة منتور — سر القوة والجمال من الأعماق</h3>
            <p>دي مش مجرد طحينة، دي تحفة غذائية متكاملة. غنية بالبروتين، الدهون الصحية (أوميجا 3)، والكالسيوم، الزنك، النحاس، والمغنيسيوم. بقوامها الناعم وطعمها الغني، بتديلك قوة من جواكي هتبان على جمالك وصحتك من بره.</p>
            <ul class="bullets">
              <li><strong>عضلات قوية وامتصاص بروتين ممتاز:</strong> بتساعد على بناء العضلات وتقوي قدرة جسمك على الاستفادة القصوى من البروتين. <strong style="color:var(--gold);">مش هتحتاجي مكملات بروتين زيادة.</strong></li>
              <li><strong>عضم قوي، بشرة منورة، شعر بيلمع:</strong> مليانة كالسيوم ومعادن أساسية، عشان تقوي عضمك وتغذي بشرتك وشعرك من الجذور. <strong style="color:var(--gold);">جمالك هيطلع من صحتك.</strong></li>
              <li><strong>راحة بال ومود أحسن:</strong> بتدعم صحة الجهاز العصبي، وبتحسن المود، وبتساعدك تتخلصي من التوتر والقلق. <strong style="color:var(--gold);">عشان تنامي نوم عميق وتصحي ذهنك رايق.</strong></li>
            </ul>
            <div class="compare">
              <strong>ليه أحسن من أي حاجة تانية؟</strong>
              <span>أحسن من المكملات الغذائية اللي بتبقى متخصصة للجمال أو مكملات النوم، عشان تركيبتها طبيعية ومتكاملة. <strong style="color:var(--gold);">دي رفيقتك لرحلة صحة متكاملة.</strong></span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- الثنائي الخارق -->
    <div class="duo">
      <h3 style="margin-top:0">اكتشف <span style="background: var(--gold-grad); -webkit-background-clip: text; background-clip:text; color: transparent;">الثنائي الخارق</span> — قوة مضاعفة، وفوائد مالهاش عدد</h3>
      <p class="lead">
        تخيل قوة دبس منتور النقي مخلوطة بكنوز طحينة منتور. معلقتين من كل واحد يومياً بيدوا لجسمك تركيبة غذائية خرافية: حديد، كالسيوم، مغنيسيوم، بوتاسيوم، زنك، نحاس، مجموعة فيتامين B الكاملة، ومضادات أكسدة.
      </p>
      <ul class="bullets">
        <li><strong>امتصاص مثالي:</strong> الزنك والنحاس اللي في الطحينة بيضاعفوا امتصاص الحديد من الدبس. <strong style="color:var(--gold);">عشان تستفيد أقصى استفادة ممكنة.</strong></li>
        <li><strong>تحكم بسكر الدم:</strong> الألياف والدهون الصحية في الطحينة بتبطئ امتصاص السكريات من الدبس. <strong style="color:var(--gold);">تأثير ممتاز على سكر الدم، حتى لمرضى السكر.</strong></li>
        <li><strong>توازن هرموني شامل:</strong> الخلطة دي بتدعم التوازن الهرموني، بتقوي الخصوبة، وبتقلل أعراض الدورة الشهرية وانقطاع الطمث. <strong style="color:var(--gold);">عشان تحس بالقوة والراحة في كل مرحلة في حياتك.</strong></li>
      </ul>
      <p class="lead" style="text-align:center;">
        <strong style="color:var(--gold);">مش مجرد خلطة، دي خطة غذائية متكاملة بتغير صحتك تماماً.</strong>
      </p>
    </div>
  </div>
</section>

  <!-- 5) Why Mentor? - ليه منتجاتنا هي اختيارك الأذكى؟ -->
  <section class="section" id="why">
  <div class="container">
    <div class="title">
      <h5>اختيارك الصح لحياة أحسن</h5>
      <h2>ليه منتجات Mentor هي <span style="background: var(--gold-grad); -webkit-background-clip: text; background-clip:text; color: transparent;">الاستثمار الأفضل لصحتك؟</span></h2>
      <p class="lead">في سوق مليان اختيارات، منتجات Mentor بتبرز كاختيار ملوش منافس. شوف إيه اللي بيميزنا:</p>
    </div>

    <div class="why-grid">
      <div class="why-card">
        <div class="icon-grid">
          <div class="icon-item">
            <span class="dot"></span>
            <div>
              <strong>نقاء مطلق ملوش منافس <span style="color:var(--gold);">(ضمان طبيعي 100%)</span></strong>
              <div class="lead">خالية تماماً من أي مواد حافظة، سكريات زيادة، زيوت مهدرجة، أو معادن تقيلة. <strong>بنضمنلك أعلى مستويات النقاء.</strong></div>
            </div>
          </div>
          <div class="icon-item">
            <span class="dot"></span>
            <div>
              <strong>تقنية معالجة فيزيائية حصرية <span style="color:var(--gold);">(علم بيخدم الطبيعة)</span></strong>
              <div class="lead">بدل المعالجة الحرارية اللي بتدمر الفايدة، بنستخدم تقنية فريدة بتحافظ على <strong style="color:var(--gold);">98% من القيمة الغذائية الأصلية</strong> لكل نقطة.</div>
            </div>
          </div>
          <div class="icon-item">
            <span class="dot"></span>
            <div>
              <strong>قيمة غذائية أحسن من المكملات <span style="color:var(--gold);">(استثمار في الصحة)</span></strong>
              <div class="lead">تركيبة غذائية مكثفة تفوق كتير من المكملات الصيدلانية وأكل الأطفال والرياضيين. <strong style="color:var(--gold);">هتشوف نتايج حقيقية وهتحس بفايدة واضحة.</strong></div>
            </div>
          </div>
          <div class="icon-item">
            <span class="dot"></span>
            <div>
              <strong>طعم فاخر يرضي الحواس <span style="color:var(--gold);">(الصحة الحلوة)</span></strong>
              <div class="lead">تجربة طعم استثنائية هتخليك تستمتع بأكل منتجاتنا كل يوم، مش مجرد واجب صحي. <strong>صحة بطعم الرفاهية.</strong></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Testimonials/Partners - كلام عملاؤنا -->
      <div class="testi-card">
        <h3 style="margin-top:0; color:var(--gold)">إيه رأي عملائنا <span style="font-size:0.8em;">(قصص تغيير حقيقية)</span></h3>
        <p class="lead">
          آلاف العملاء بيشاركونا قصص نجاحهم مع منتجات Mentor. من استعادة الطاقة اللي كانت مفقودة لتحسين الصحة والجمال بشكل عام، منتجاتنا بتعمل فرق حقيقي في حياتهم اليومية.
        </p>
        <div class="customer-testimonials" style="grid-template-columns: 1fr;">
            <div class="testimonial-card">
                <p>"بعد كام أسبوع من استخدام دبس منتور، حسيت بطاقة مكنتش حساها من سنين. خلاص مفيش تعب تاني!"</p>
                <strong>ليلى م.</strong><span> (34 سنة، مديرة تسويق)</span>
            </div>
            <div class="testimonial-card">
                <p>"طحينة منتور غيرت بشرتي وشعري تماماً. بقيت أحلى ومنورة أكتر. أنصح بيها أي ست!"</p>
                <strong>فاطمة ح.</strong><span> (29 سنة، بلوجر جمال)</span>
            </div>
            <div class="testimonial-card">
                <p>"الثنائي الخارق ده سر قوتي في الجيم. طاقة جامدة وبعمل ريكفري أسرع. مبقدرش أستغنى عنه أبداً!"</p>
                <strong>أحمد س.</strong><span> (26 سنة، رياضي)</span>
            </div>
        </div>
      </div>
    </div>
  </div>
</section>

  <!-- 6) Call to Action - يلا بينا ناخد خطوة -->
  <section class="section fold" id="cta">
  <div class="container" style="text-align:center;">
    <div class="title">
      <h5>جه وقت التغيير</h5>
      <h2>متترددش — <span style="background: var(--gold-grad); -webkit-background-clip: text; background-clip:text; color: transparent;">جرب منتجات Mentor النهارده!</span></h2>
      <p class="lead">رحلتك لصحة أحسن وطاقة متجددة بتبدأ بخطوة واحدة. اكتشف الفرق اللي منتجاتنا الأصلية والطبيعية بتعمله.</p>
    </div>
    <div class="cta-row">
      <a href="https://adamsuperfoods.com/shop" target="_blank" class="btn gold">
          <div class="wrap">
              <p>
                  <span>تسوق دلوقتي <span style="font-size:90%">🛒</span></span>
                  <span>ابدأ رحلتك <span style="font-size:90%">🚀</span></span>
              </p>
          </div>
      </a>
      <a href="chrome://new-tab-page/#contact" class="btn">
          <div class="wrap">
              <p>
                  <span>اسأل على أقرب مكان تبيع فيه</span>
                  <span>كلمنا <span style="font-size:90%">📞</span></span>
              </p>
          </div>
      </a>
    </div>
  </div>
</section>


  <!-- 7) Contact - للتواصل والمعلومات الختامية -->
  <footer class="section" id="contact">
  <div class="container">
    <div class="title">
      <h5>كلمنا</h5>
      <h2>صحتك تهمنا — <span style="background: var(--gold-grad); -webkit-background-clip: text; background-clip:text; color: transparent;">إحنا هنا عشان نخدمك</span></h2>
    </div>

    <div class="contact-info">
      <p class="lead" style="margin-top:0">
        عندك أي أسئلة عن منتجاتنا أو حابب تعرف أكتر عن فوايدها؟ فريق خدمة العملاء بتاعنا جاهز يساعدك.
      </p>
      <div class="sep"></div>
      <p><strong>التليفون:</strong> <a href="tel:+201001103654">+201001103654</a></p>
      <p><strong>الإيميل:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></p>
      <p><strong>الموقع الرسمي:</strong> <a href="https://adamsuperfoods.com/" target="_blank" rel="noopener">adamsuperfoods.com</a></p>
      
      <div class="social-links">
        <a href="https://facebook.com/adamsuperfoods" target="_blank" class="social-link" title="Facebook">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.27 0-3.952 1.609-3.952 4.093v2.907z"></path></svg>
        </a>
        <a href="https://instagram.com/adamsuperfoods" target="_blank" class="social-link" title="Instagram">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.204-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.012-3.584.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.271-.073 1.679-.073 4.948 0 3.26.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.271.058 1.679.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.271.073-1.679.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.271-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.162 6.162 6.162 6.162-2.759 6.162-6.162c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.791-4-4s1.791-4 4-4 4 1.791 4 4c0 2.208-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"></path></svg>
        </a>
      </div>
      <p style="font-size:0.9em; margin-top:20px;">© 2025 آدم سوبرفودز. جميع الحقوق محفوظة.</p>
    </div>
  </div>
</footer>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تفعيل تأثير "طي وفتح الكتاب"
        const productBooks = document.querySelectorAll('.product-card.book');

        productBooks.forEach(book => {
            // للتعامل مع أجهزة اللمس
            book.addEventListener('click', function(event) {
                // منع النقر على الروابط أو العناصر التفاعلية داخل الكتاب من إغلاقه
                if (event.target.closest('a') || event.target.closest('button')) {
                    return;
                }
                this.classList.toggle('active'); // تبديل فئة 'active' عند النقر
            });

            // للتعامل مع التمرير بالماوس (الأجهزة المكتبية)
            book.addEventListener('mouseenter', function() {
                this.classList.add('active');
            });
            book.addEventListener('mouseleave', function() {
                this.classList.remove('active');
            });
        });
    });
</script>


</body></html>