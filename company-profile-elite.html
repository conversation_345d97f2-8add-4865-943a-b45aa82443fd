<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>شركة آدم للمواد الغذائية — ملف تعريفي فاخر</title>

  <!-- Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@300;400;600;700;800;900&display=swap" rel="stylesheet"/>

  <style>
    :root{
      --black:#0a0a0a;
      --nearBlack:#121212;
      --dark:#151515;
      --gold:#c9a35a;
      --gold-strong:#feb406;
      --gold-deep:#8f6B29;
      --coffee:#b8945a;
      --coffee-light:#c4a484;
      --white:#ffffff;
      --muted:#d9d7cf;
      --text:#f7f6f1;
      --text-dim:#cfcac0;
      --shadow-strong: 0 20px 40px rgba(0,0,0,.45);
      --shadow-soft: 0 10px 25px rgba(0,0,0,.25);
      --glow-gold: 0 0 35px rgba(201,163,90,.4);
      --glow-gold-strong: 0 0 45px rgba(201,163,90,.6);
      --shine: linear-gradient(90deg, transparent, rgba(255,255,255,.25), transparent);
      --gold-grad: linear-gradient(135deg, #b78f4a 0%, #e2c079 35%, #c19a5d 60%, #8f6B29 100%);
      --gold-grad-hover: linear-gradient(135deg, #c9a35a 0%, #f1d08a 35%, #d1aa6d 60%, #a07c39 100%);
      --page-padding: min(6vw,60px);
      --radius: 14px;
      --radius-lg: 20px;
      --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    html,body{
      margin:0; padding:0;
      background: var(--black);
      color: var(--text);
      font-family: "Noto Kufi Arabic", system-ui, -apple-system, Segoe UI, Roboto, Ubuntu, "Helvetica Neue", Arial, "Apple Color Emoji", "Segoe UI Emoji";
      line-height:1.7;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    .container{
      width: min(1180px, 100%);
      margin: 0 auto;
      padding-inline: var(--page-padding);
    }

    .section{
      padding: clamp(40px, 6vw, 90px) 0;
      position: relative;
      isolation: isolate;
    }

    .title{
      margin-bottom: clamp(24px,3vw,40px);
      position: relative;
    }
    .title h2{
      font-weight:800;
      font-size: clamp(26px, 3.4vw, 40px);
      letter-spacing:.4px;
      color: var(--text);
      margin: 0 0 10px;
      position: relative;
      display: inline-block;
    }
    .title h2::after {
      content: "";
      position: absolute;
      left: 0;
      right: 0;
      bottom: -5px;
      height: 1px;
      background: linear-gradient(90deg, var(--gold), transparent);
      opacity: 0.4;
    }
    .title h5{
      color: var(--gold);
      margin:0 0 8px;
      font-weight:700;
      letter-spacing: .6px;
      position: relative;
      display: inline-block;
    }
    .title h5::before {
      content: "";
      position: absolute;
      left: -15px;
      top: 50%;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: var(--gold);
      transform: translateY(-50%);
      opacity: 0.7;
    }
    .lead{
      color: var(--text-dim);
      font-size: clamp(14px, 1.4vw, 16.5px);
      line-height: 1.8;
    }

    /* Cover (الغلاف) */
    .cover{
      min-height: 86vh;
      display:flex;
      align-items:center;
      background:
        radial-gradient(1200px 500px at 70% 10%, rgba(255,214,137,.1), transparent 60%),
        radial-gradient(900px 600px at 15% 80%, rgba(255,214,137,.08), transparent 60%),
        var(--nearBlack);
      position: relative;
      overflow: hidden;
    }
    .cover::before{
      content:"";
      position:absolute; inset:0;
      background: url("https://adamsuperfoods.com/static/media/logo-light.4b0e8ebc9a1852861efd.png") center/180px no-repeat;
      opacity:.03;
      filter: grayscale(1);
      pointer-events:none;
      animation: pulse-subtle 8s ease-in-out infinite;
    }
    @keyframes pulse-subtle {
      0%, 100% { opacity: .03; }
      50% { opacity: .05; }
    }
    .cover .wrap{
      display:grid; gap: clamp(18px,3vw,30px);
      grid-template-columns: 1.1fr .9fr;
    }
    .brand-lockup{
      display:flex; align-items:center; gap:16px;
      color: var(--gold);
      text-transform: uppercase;
      letter-spacing:1.4px;
      font-weight:700;
      position: relative;
      z-index: 2;
    }
    .brand-lockup img{
      width:56px; height:56px; object-fit:contain; 
      filter: drop-shadow(0 6px 12px rgba(0,0,0,.35));
      animation: float 6s ease-in-out infinite;
    }
    @keyframes float {
      0%, 100% { transform: translateY(0); }
      50% { transform: translateY(-5px); }
    }
    .cover h1{
      font-weight:900; margin:6px 0 12px;
      font-size: clamp(28px,4.5vw,56px);
      line-height:1.15;
      background: var(--gold-grad);
      -webkit-background-clip: text; background-clip:text;
      color: transparent;
      text-shadow: 0 0 0 rgba(0,0,0,0.1);
      position: relative;
      z-index: 2;
    }
    .cover h1::after {
      content: "";
      position: absolute;
      left: -5px;
      right: -5px;
      bottom: -8px;
      height: 1px;
      background: linear-gradient(90deg, transparent, var(--gold), transparent);
      opacity: 0.6;
    }
    .tagline{
      font-size: clamp(14px,1.6vw,18px);
      color: var(--text-dim);
      max-width: 62ch;
      position: relative;
      z-index: 2;
    }
    .hero-art{
      position:relative;
      border-radius: var(--radius-lg);
      background: linear-gradient(180deg, rgba(255,255,255,.06), rgba(255,255,255,.02));
      border: 1px solid rgba(201,163,90,.18);
      box-shadow: var(--glow-gold), var(--shadow-strong);
      height: clamp(320px, 40vw, 520px);
      overflow:hidden;
      transition: var(--transition-smooth);
    }
    .hero-art:hover {
      box-shadow: var(--glow-gold-strong), var(--shadow-strong);
      border: 1px solid rgba(201,163,90,.25);
      transform: translateY(-5px);
    }
    .hero-art .glow{
      position:absolute; inset:0;
      background:
        radial-gradient(280px 220px at 70% 30%, rgba(254,180,6,.18), transparent 60%),
        radial-gradient(380px 220px at 20% 80%, rgba(254,180,6,.15), transparent 65%);
      filter: blur(6px);
      pointer-events:none;
      animation: pulse-glow 8s ease-in-out infinite;
    }
    @keyframes pulse-glow {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.7; }
    }
    .hero-art .shine{
      position:absolute; inset:0;
      background: linear-gradient(115deg, transparent 0 40%, rgba(255,255,255,.22) 45%, transparent 55% 100%);
      transform: translateX(-120%);
      animation: shine 4.5s ease-in-out 1.2s infinite;
      mix-blend-mode: screen;
    }
    @keyframes shine{
      0%{ transform: translateX(-120%);}
      60%{ transform: translateX(120%);}
      100%{ transform: translateX(120%);}
    }
    .hero-art .main-shot{
      position:absolute; inset:0;
      display:flex; align-items:center; justify-content:center;
    }
    .product-duo{
      width: min(86%, 760px); aspect-ratio: 16/9;
      margin:auto; border-radius: 14px;
      background:
        radial-gradient(300px 200px at 70% 20%, rgba(255,255,255,.08), transparent 60%),
        #0d0d0d url("https://adamsuperfoods.com/static/media/logo-light.4b0e8ebc9a1852861efd.png") center/160px no-repeat;
      border: 1px solid rgba(255,255,255,.06);
      position: relative; overflow:hidden;
      box-shadow: var(--shadow-strong);
    }
    /* Reflection effect */
    .product-duo::after{
      content:"";
      position:absolute; left:8%; right:8%; bottom:-6%;
      height:18%;
      background: linear-gradient(180deg, rgba(255,255,255,.15), rgba(255,255,255,0));
      filter: blur(10px); opacity:.35; transform: skewY(-3deg);
    }

    .cta-row{
  display:flex; gap:14px; flex-wrap: wrap; margin-top: 20px;
}
.btn{
  padding: 12px 24px; 
  border-radius: 999px;
  border:1px solid rgba(201,163,90,.45); 
  color: var(--text);
  background: linear-gradient(180deg, rgba(255,255,255,.08), rgba(255,255,255,.03));
  box-shadow: 0 6px 16px rgba(0,0,0,.35);
  text-decoration:none; 
  font-weight:700; 
  letter-spacing:.3px;
  transition: var(--transition-smooth);
  position: relative;
  overflow: hidden;
  z-index: 1;
}
.btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, rgba(255,255,255,.1), rgba(255,255,255,.04));
  transform: translateY(100%);
  transition: var(--transition-smooth);
  z-index: -1;
}
.btn:hover{ 
  transform: translateY(-2px); 
  box-shadow: 0 12px 24px rgba(0,0,0,.45);
  border-color: rgba(201,163,90,.6);
}
.btn:hover::before {
  transform: translateY(0);
}
.btn.gold{
  background: var(--gold-grad); 
  color: #1b1b19; 
  border-color: transparent;
  position: relative;
}
.btn.gold::before {
  background: var(--gold-grad-hover);
  opacity: 0;
}
.btn.gold:hover::before {
  opacity: 1;
}

    /* Section separators (folds) */
    .fold{
      position: relative; overflow: clip;
    }
    .fold::before{
      content:"";
      position:absolute; inset:0 -20vw auto -20vw; height: 72px;
      background: linear-gradient(180deg, rgba(255,255,255,.08), rgba(255,255,255,0));
      transform: skewY(-3deg); opacity:.3;
      animation: pulse-fold 10s ease-in-out infinite;
    }
    @keyframes pulse-fold {
      0%, 100% { opacity: .3; }
      50% { opacity: .2; }
    }

    /* Big Hook */
    .big-hook{
      background:
        radial-gradient(600px 300px at 85% 20%, rgba(254,180,6,.12), transparent 55%),
        radial-gradient(700px 300px at 15% 80%, rgba(254,180,6,.08), transparent 60%),
        var(--dark);
      border-top: 1px solid rgba(255,255,255,.06);
      border-bottom: 1px solid rgba(255,255,255,.06);
    }
    .hook-card{
      border: 1px solid rgba(201,163,90,.28);
      background: linear-gradient(180deg, rgba(255,255,255,.05), rgba(255,255,255,.02));
      border-radius: var(--radius);
      padding: clamp(18px,2.8vw,28px);
      box-shadow: var(--shadow-soft);
    }

    /* About (Timeline + Values) */
    .about-grid{
      display:grid;
      gap: clamp(18px,3vw,32px);
      grid-template-columns: 1.1fr .9fr;
      align-items: start;
    }
    .about-card, .photo-card{
      border: 1px solid rgba(201,163,90,.25);
      background: linear-gradient(180deg, rgba(255,255,255,.04), rgba(255,255,255,.02));
      border-radius: var(--radius);
      padding: clamp(18px,2.8vw,28px);
      box-shadow: var(--shadow-soft);
    }
    .photo-card{
      min-height: 320px;
      background:
        radial-gradient(240px 180px at 20% 20%, rgba(254,180,6,.14), transparent 60%),
        #0f0f0f url("https://adamsuperfoods.com/static/media/logo-light.4b0e8ebc9a1852861efd.png") center/120px no-repeat;
      position:relative;
    }
    .photo-card .frame{
      position:absolute; inset:10px;
      border:1px solid rgba(201,163,90,.45);
      border-radius: 12px;
      box-shadow: inset 0 0 0 1px rgba(255,255,255,.04), 0 0 0 1px rgba(0,0,0,.3);
    }

    .timeline{
      display:grid; gap:16px; margin-top: 16px;
    }
    .milestone{
      display:grid; gap:8px;
      border-left: 3px solid var(--gold);
      padding-left: 12px;
    }
    .milestone strong{ color: var(--gold); }

    .values{
      display:grid; grid-template-columns: repeat(2,1fr); gap:10px; margin-top: 16px;
    }
    .chip{
      border:1px solid rgba(201,163,90,.28);
      background: linear-gradient(180deg, rgba(255,255,255,.05), rgba(255,255,255,.02));
      color: var(--text);
      padding:10px 12px; border-radius: 10px; font-weight:700;
      box-shadow: var(--shadow-soft);
    }

    /* Products & Solutions */
    .product-split{
      display:grid; gap: clamp(18px,3vw,28px);
      grid-template-columns: 1fr 1fr;
      align-items: center;
    }
    .product-card {
  border-radius: var(--radius-lg);
  background: linear-gradient(180deg, rgba(255,255,255,.04), rgba(255,255,255,.01));
  border: 1px solid rgba(201,163,90,.15);
  overflow: hidden;
  transition: var(--transition-smooth);
  box-shadow: var(--shadow-soft);
}
.product-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-strong), var(--glow-gold);
  border-color: rgba(201,163,90,.25);
}
    .shot{
  height: clamp(220px, 28vw, 360px);
  background:
    radial-gradient(240px 160px at 70% 20%, rgba(254,180,6,.12), transparent 60%),
    #101010;
  position:relative;
  border-bottom: 1px solid rgba(255,255,255,.06);
}
/* Shine line over image */
.shot::before{
  content:"";
  position:absolute; inset:0;
  background: var(--shine);
  transform: translateX(-120%);
  animation: shine 6s ease-in-out 1s infinite;
  mix-blend-mode: screen;
}
.product-card .shot {
  position: relative;
  overflow: hidden;
}
.product-card .shot::after {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(115deg, transparent 0 40%, rgba(255,255,255,.15) 45%, transparent 55% 100%);
  transform: translateX(-120%);
  animation: shine 6s ease-in-out 2s infinite;
  mix-blend-mode: screen;
}
.product-card .note {
  position: absolute;
  bottom: 15px;
  right: 15px;
  background: rgba(10,10,10,.85);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(201,163,90,.2);
  padding: 8px 15px;
  border-radius: 30px;
  font-size: 14px;
  color: var(--gold);
  box-shadow: var(--shadow-soft);
}
.shot .note{
  position:absolute; inset:auto 12px 12px auto;
  background: rgba(0,0,0,.5);
  border:1px solid rgba(201,163,90,.35);
  color: var(--muted);
  padding: 6px 10px; border-radius: 8px; font-size: 12px;
  backdrop-filter: blur(6px);
}
    .p-body{ padding: clamp(16px,2.2vw,22px); }
    .p-body h3{ margin:0 0 8px; font-size: clamp(18px,2.2vw,24px); }
    .bullets{
      display:grid; gap:8px; margin: 10px 0 0;
    }
    .bullets li{ color: var(--text-dim); }
    .compare{
      margin-top: 12px; display:grid; gap:8px;
      background: rgba(201,163,90,.06);
      border:1px dashed rgba(201,163,90,.35);
      border-radius: 12px; padding:12px;
    }
    .compare strong{ color: var(--gold); }

    /* Duo Power */
    .duo{
      margin-top: clamp(10px, 2vw, 18px);
      border: 1px solid rgba(201,163,90,.25);
      background: linear-gradient(180deg, rgba(255,255,255,.03), rgba(255,255,255,.01));
      border-radius: var(--radius);
      padding: clamp(14px,2vw,18px);
    }

    /* Why Us + Testimonials */
    .why-grid{
      display:grid; gap: clamp(18px,3vw,28px);
      grid-template-columns: 1fr 1fr;
    }
    .testi-card{
  border: 1px solid rgba(201,163,90,.25);
  background: linear-gradient(180deg, rgba(255,255,255,.04), rgba(255,255,255,.02));
  border-radius: var(--radius);
  padding: clamp(18px,2.8vw,28px);
  box-shadow: var(--shadow-soft);
  transition: var(--transition-smooth);
  opacity: 0.4;
  transform: scale(0.9);
}

.testi-card.active {
  opacity: 1;
  transform: scale(1);
}

.testi-slider {
  position: relative;
  overflow: hidden;
  margin-top: 20px;
}

.testi-track {
  display: flex;
  transition: transform 0.5s ease;
}

.testi-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.testi-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--gold-deep), var(--gold));
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-soft);
}

.avatar-placeholder {
  color: var(--dark);
  font-weight: 700;
  font-size: 18px;
}

.testi-header h4 {
  margin: 0;
  color: var(--gold);
  font-size: 18px;
}

.testi-position {
  margin: 0;
  color: var(--text-dim);
  font-size: 14px;
}

.testi-quote {
  position: relative;
  margin-bottom: 20px;
}

.testi-quote::before {
  content: '"';
  position: absolute;
  top: -15px;
  right: -5px;
  font-size: 60px;
  color: var(--gold);
  opacity: 0.2;
  font-family: serif;
}

.testi-metrics {
  display: flex;
  gap: 20px;
  margin-top: 15px;
  border-top: 1px solid rgba(201,163,90,.15);
  padding-top: 15px;
}

.metric {
  flex: 1;
  text-align: center;
}

.metric-value {
  font-size: 24px;
  font-weight: 800;
  color: var(--gold);
  margin-bottom: 5px;
}

.metric-label {
  font-size: 14px;
  color: var(--text-dim);
}

.testi-nav {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  margin-top: 20px;
}

.testi-dots {
  display: flex;
  gap: 8px;
}

.testi-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: rgba(201,163,90,.2);
  border: none;
  padding: 0;
  cursor: pointer;
  transition: var(--transition-smooth);
}

.testi-dot.active {
  background: var(--gold);
  transform: scale(1.2);
}

.testi-btn {
  background: linear-gradient(180deg, rgba(255,255,255,.05), rgba(255,255,255,.02));
  border: 1px solid rgba(201,163,90,.25);
  color: var(--gold);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-smooth);
}

.testi-btn:hover {
  background: linear-gradient(180deg, rgba(255,255,255,.08), rgba(255,255,255,.04));
  border-color: rgba(201,163,90,.4);
  transform: translateY(-2px);
}
.why-card {
  border-radius: var(--radius-lg);
  background: linear-gradient(180deg, rgba(255,255,255,.04), rgba(255,255,255,.01));
  border: 1px solid rgba(201,163,90,.15);
  padding: 25px;
  transition: var(--transition-smooth);
  box-shadow: var(--shadow-soft);
}
.why-card:hover {
  box-shadow: var(--shadow-strong), var(--glow-gold);
  border-color: rgba(201,163,90,.25);
}
    .icon-grid{
      display:grid; gap:10px;
      grid-template-columns: repeat(2,1fr);
    }
    .icon-item {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  border:1px solid rgba(201,163,90,.25);
  background: linear-gradient(180deg, rgba(255,255,255,.04), rgba(255,255,255,.02));
  padding: 10px 12px; border-radius: 12px;
  box-shadow: var(--shadow-soft);
}
.icon-item .dot {
  width: 12px;
  height: 12px;
  background: var(--gold);
  border-radius: 50%;
  margin-top: 8px;
  position: relative;
  flex: 0 0 12px;
}
.icon-item .dot::after {
  content: "";
  position: absolute;
  inset: -4px;
  border-radius: 50%;
  border: 1px solid var(--gold);
  opacity: 0.4;
}
.dot{
  width:10px; height:10px; border-radius:50%;
  background: var(--gold);
  box-shadow: 0 0 8px rgba(201,163,90,.7);
  margin-top: 5px;
  flex: 0 0 10px;
}

    .partners{
      display:flex; gap:22px; overflow:auto; padding: 8px 4px 4px;
      scroll-snap-type:x mandatory;
    }
    .brand{
      min-width: 120px; height: 60px; border-radius: 10px;
      border:1px solid rgba(255,255,255,.08);
      background: #0f0f0f url("https://adamsuperfoods.com/static/media/logo-light.4b0e8ebc9a1852861efd.png") center/contain no-repeat;
      filter: grayscale(0.1) contrast(.98);
      opacity:.9;
      scroll-snap-align: start;
      box-shadow: var(--shadow-soft);
    }

    /* Success Stories */
    .stories{
      display:grid; gap: clamp(14px, 1.8vw, 18px);
      grid-template-columns: repeat(3, 1fr);
    }
    .story{
      border:1px solid rgba(201,163,90,.25);
      background: linear-gradient(180deg, rgba(255,255,255,.04), rgba(255,255,255,.02));
      border-radius: var(--radius);
      padding: clamp(14px,2vw,18px);
      box-shadow: var(--shadow-soft);
    }
    .story h4{ margin:0 0 6px; color: var(--gold); }

    /* Partner With Us */
    .partner{
      position: relative;
      overflow:hidden;
      border: 1px solid rgba(201,163,90,.25);
      background:
        radial-gradient(420px 240px at 80% 20%, rgba(254,180,6,.12), transparent 60%),
        #0f0f0f url("phttps://adamsuperfoods.com/static/media/logo-light.4b0e8ebc9a1852861efd.png") center/120px no-repeat;
      border-radius: var(--radius);
      box-shadow: var(--shadow-soft);
    }
    .partner .overlay{
      position:absolute; inset:0; background: rgba(0,0,0,.6);
      backdrop-filter: blur(2px);
    }
    .partner .inner{
      position:relative; z-index:2; padding: clamp(18px, 2.6vw, 28px);
    }

    /* Contact */
    .contact{
      border: 1px solid rgba(201,163,90,.25);
      background: linear-gradient(180deg, rgba(255,255,255,.04), rgba(255,255,255,.02));
      border-radius: var(--radius);
      box-shadow: var(--shadow-soft);
    }
    .contact-grid {
      display: grid;
      grid-template-columns: 1.5fr 1fr;
      gap: 40px;
      align-items: center;
      padding: clamp(18px, 2.6vw, 28px);
    }
    .contact-grid a {
      transition: var(--transition-smooth);
      position: relative;
    }
    .contact-grid a::after {
      content: "";
      position: absolute;
      left: 0;
      right: 0;
      bottom: -2px;
      height: 1px;
      background: var(--gold);
      transform: scaleX(0);
      transform-origin: right;
      transition: var(--transition-smooth);
      opacity: 0.7;
    }
    .contact-grid a:hover {
      color: var(--gold-strong) !important;
    }
    .contact-grid a:hover::after {
      transform: scaleX(1);
      transform-origin: left;
    }
    .qr {
      width: 180px;
      height: 180px;
      margin-left: auto;
      background: var(--white) url("https://chart.googleapis.com/chart?chs=160x160&cht=qr&chl=https%3A%2F%2Fadamsuperfoods.com%2Fcatalog") center/cover no-repeat;
      border-radius: var(--radius);
      position: relative;
      box-shadow: var(--shadow-soft);
      transition: var(--transition-smooth);
      border: none;
    }
    .qr:hover {
      transform: scale(1.05);
      box-shadow: var(--shadow-strong), var(--glow-gold);
    }
    .qr::before {
      content: "";
      position: absolute;
      inset: -5px;
      border: 1px solid var(--gold);
      border-radius: calc(var(--radius) + 5px);
      opacity: 0.3;
    }

    /* Utility */
    .sep{
      height:1px; background: linear-gradient(90deg, transparent, rgba(201,163,90,.4), transparent);
      margin: 16px 0;
    }

    /* Visualizations (Mindmaps, Flowcharts, Charts) */
    .viz-grid{
      display:grid; gap: clamp(18px,3vw,28px);
      grid-template-columns: 1.2fr .8fr;
      align-items: stretch;
    }
    .viz-card{
      border: 1px solid rgba(201,163,90,.25);
      background: linear-gradient(180deg, rgba(255,255,255,.04), rgba(255,255,255,.02));
      border-radius: var(--radius);
      padding: clamp(16px,2.4vw,22px);
      box-shadow: var(--shadow-soft);
    }
    .viz-title{
      margin:0 0 10px; color: var(--gold); font-weight:800;
    }
    .viz-note{ color: var(--text-dim); font-size: .95rem; }

    /* Simple Mindmap/Flowchart SVG helpers */
    .mindmap, .flowchart{
      position:relative; overflow:hidden; border-radius: 12px;
      border:1px solid rgba(201,163,90,.22);
      background: radial-gradient(220px 160px at 70% 20%, rgba(254,180,6,.10), transparent 60%), #0f0f0f;
      padding: 10px;
    }
    .mindmap svg, .flowchart svg{ width:100%; height:360px; display:block; }
    .node{
      fill: rgba(255,255,255,.06);
      stroke: rgba(201,163,90,.32);
      stroke-width: 1.2;
    }
    .node text{
      fill: var(--text);
      font-weight:700;
      font-size: 12.5px;
    }
    .edge{ stroke: rgba(201,163,90,.35); stroke-width:1.2; marker-end: url(#arrow); }

    /* Bars / KPIs */
    .bars{
      display:grid; gap:10px; margin-top:10px;
    }
    .bar{
      display:flex; align-items:center; gap:10px;
    }
    .bar .label{
      flex: 0 0 170px; color: var(--text-dim); font-weight:700;
    }
    .bar .track{
      flex:1; height:12px; border-radius: 999px; background: rgba(255,255,255,.06); overflow:hidden;
      border:1px solid rgba(201,163,90,.18);
    }
    .bar .fill{
      height:100%; background: var(--gold-grad);
      box-shadow: inset 0 1px 0 rgba(255,255,255,.12), 0 6px 14px rgba(0,0,0,.25);
    }

    .kpi-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 30px;
      margin-top: 40px;
    }
    .kpi {
      text-align: center;
      padding: 25px;
      border-radius: var(--radius);
      background: linear-gradient(180deg, rgba(255,255,255,.04), rgba(255,255,255,.01));
      border: 1px solid rgba(201,163,90,.15);
      transition: var(--transition-smooth);
      box-shadow: var(--shadow-soft);
    }
    .kpi:hover {
      transform: translateY(-5px);
      box-shadow: var(--shadow-soft), var(--glow-gold);
      border-color: rgba(201,163,90,.25);
    }
    .kpi .num {
      font-size: clamp(36px, 5vw, 48px);
      font-weight: 800;
      color: var(--gold);
      margin-bottom: 5px;
      background: var(--gold-grad);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
    }
    .kpi .lbl {
      color: var(--text-dim);
      font-size: 15px;
    }

    .quality{
      margin-top: 14px;
      border: 1px solid rgba(201,163,90,.28);
      background: linear-gradient(180deg, rgba(255,255,255,.05), rgba(255,255,255,.02));
      border-radius: 12px; padding: 14px;
    }
    .quality ul{ margin:0; padding-inline-start: 18px; }
    .quality li{ color: var(--text-dim); margin: 6px 0; }

    .partnership-form {
      margin-top: 30px;
    }

    .contact-form {
      margin-top: 20px;
    }

    .form-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
    }

    .form-group.full {
      grid-column: 1 / -1;
    }

    .form-group label {
      display: block;
      margin-bottom: 8px;
      color: var(--gold);
      font-weight: 600;
    }

    .required {
      color: #e57373;
    }

    .contact-form input,
    .contact-form select,
    .contact-form textarea {
      width: 100%;
      padding: 12px 15px;
      background: rgba(255,255,255,.03);
      border: 1px solid rgba(201,163,90,.25);
      border-radius: var(--radius-sm);
      color: var(--text);
      transition: var(--transition-smooth);
    }

    .contact-form input:focus,
    .contact-form select:focus,
    .contact-form textarea:focus {
      outline: none;
      border-color: rgba(201,163,90,.5);
      box-shadow: 0 0 0 2px rgba(201,163,90,.1);
    }

    .checkbox-group {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;
    }

    .checkbox {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
    }

    .checkbox input {
      width: auto;
    }

    .form-footer {
      margin-top: 25px;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .form-note {
      margin-top: 10px;
      font-size: 14px;
      color: var(--text-dim);
    }

    @media (max-width: 768px) {
      .form-grid,
      .checkbox-group {
        grid-template-columns: 1fr;
      }
    }

    /* Responsive */
    @media (max-width: 980px){
      .cover .wrap,
      .about-grid,
      .product-split,
      .why-grid,
      .contact-grid{ grid-template-columns: 1fr; }
      .stories{ grid-template-columns: 1fr 1fr; }
      .viz-grid{ grid-template-columns: 1fr; }
      .kpi-grid{ grid-template-columns: 1fr 1fr; }
    }
    @media (max-width: 620px){
      .stories{ grid-template-columns: 1fr; }
      .values{ grid-template-columns: 1fr; }
      .icon-grid{ grid-template-columns: 1fr; }
      .kpi-grid{ grid-template-columns: 1fr; }
    }

    /* ROI Calculator Styles */
    .roi-calculator {
      border: 1px solid rgba(201,163,90,.28);
      background: linear-gradient(180deg, rgba(255,255,255,.04), rgba(255,255,255,.01));
      border-radius: var(--radius);
      padding: clamp(20px, 3vw, 30px);
      margin-top: 30px;
      box-shadow: var(--shadow-soft);
      transition: var(--transition-smooth);
    }
    
    .roi-calculator:hover {
      box-shadow: var(--shadow-strong), var(--glow-gold);
      border-color: rgba(201,163,90,.4);
    }
    
    .calc-grid {
      display: grid;
      grid-template-columns: 1.2fr 0.8fr;
      gap: 30px;
      margin-bottom: 20px;
    }
    
    .calc-group {
      margin-bottom: 25px;
    }
    
    .calc-group label {
      display: block;
      margin-bottom: 10px;
      color: var(--gold);
      font-weight: 600;
    }
    
    .calc-group input[type="range"] {
      width: 100%;
      height: 6px;
      background: rgba(201,163,90,.15);
      border-radius: 999px;
      outline: none;
      -webkit-appearance: none;
    }
    
    .calc-group input[type="range"]::-webkit-slider-thumb {
      -webkit-appearance: none;
      width: 18px;
      height: 18px;
      border-radius: 50%;
      background: var(--gold);
      cursor: pointer;
      border: 2px solid var(--dark);
      box-shadow: 0 0 10px rgba(201,163,90,.5);
      transition: var(--transition-smooth);
    }
    
    .calc-group input[type="range"]::-webkit-slider-thumb:hover {
      transform: scale(1.1);
    }
    
    .range-values {
      display: flex;
      justify-content: space-between;
      margin-top: 8px;
      color: var(--text-dim);
      font-size: 14px;
    }
    
    .result-card {
      background: linear-gradient(180deg, rgba(255,255,255,.03), rgba(255,255,255,.01));
      border: 1px solid rgba(201,163,90,.2);
      border-radius: var(--radius-sm);
      padding: 15px;
      margin-bottom: 15px;
      text-align: center;
      transition: var(--transition-smooth);
    }
    
    .result-card:hover {
      border-color: rgba(201,163,90,.4);
      transform: translateY(-3px);
      box-shadow: var(--shadow-soft);
    }
    
    .result-card h3 {
      margin: 0 0 10px;
      font-size: 16px;
      color: var(--text-dim);
    }
    
    .result-value {
      font-size: 28px;
      font-weight: 800;
      color: var(--gold);
      margin-bottom: 5px;
    }
    
    .result-card.highlight {
      background: linear-gradient(180deg, rgba(201,163,90,.08), rgba(201,163,90,.04));
      border-color: rgba(201,163,90,.35);
    }
    
    .result-note {
      font-size: 14px;
      color: var(--text-dim);
    }
    
    .calc-note {
      display: flex;
      gap: 10px;
      margin-top: 15px;
      color: var(--text-dim);
      font-size: 14px;
    }
    
    /* شهادات تفاعلية */
    .testi-slider {
      position: relative;
      margin-top: 30px;
      overflow: hidden;
    }
    
    .testi-track {
      position: relative;
      height: 320px;
    }
    
    .testi-card {
      position: absolute;
      inset: 0;
      opacity: 0;
      transform: translateX(40px);
      transition: all 0.5s ease-in-out;
      pointer-events: none;
    }
    
    .testi-card.active {
      opacity: 1;
      transform: translateX(0);
      pointer-events: all;
    }
    
    .testi-header {
      display: flex;
      align-items: center;
      gap: 15px;
      margin-bottom: 15px;
    }
    
    .testi-avatar {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: linear-gradient(135deg, rgba(201,163,90,.3), rgba(201,163,90,.1));
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid rgba(201,163,90,.4);
    }
    
    .avatar-placeholder {
      color: var(--gold);
      font-weight: 700;
    }
    
    .testi-position {
      color: var(--text-dim);
      font-size: 14px;
      margin: 0;
    }
    
    .testi-quote {
      position: relative;
      padding-left: 20px;
      border-left: 3px solid var(--gold);
      margin-bottom: 20px;
    }
    
    .testi-quote p {
      font-style: italic;
      color: var(--text);
    }
    
    .testi-metrics {
      display: flex;
      gap: 20px;
    }
    
    .metric {
      flex: 1;
      background: linear-gradient(180deg, rgba(255,255,255,.03), rgba(255,255,255,.01));
      border: 1px solid rgba(201,163,90,.2);
      border-radius: var(--radius-sm);
      padding: 10px;
      text-align: center;
    }
    
    .metric-value {
      font-size: 24px;
      font-weight: 800;
      color: var(--gold);
    }
    
    .metric-label {
      font-size: 14px;
      color: var(--text-dim);
    }
    
    .testi-nav {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 15px;
      margin-top: 20px;
    }
    
    .testi-btn {
      background: transparent;
      border: 1px solid rgba(201,163,90,.3);
      color: var(--gold);
      width: 36px;
      height: 36px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: var(--transition-smooth);
    }
    
    .testi-btn:hover {
      background: rgba(201,163,90,.1);
      border-color: rgba(201,163,90,.5);
    }
    
    .testi-dots {
      display: flex;
      gap: 8px;
    }
    
    .testi-dot {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background: rgba(201,163,90,.2);
      border: none;
      cursor: pointer;
      transition: var(--transition-smooth);
    }
    
    .testi-dot.active {
      background: var(--gold);
      box-shadow: 0 0 8px rgba(201,163,90,.7);
    }
    
    /* جدول المقارنة */
    .comparison-table {
      margin-top: 30px;
      border: 1px solid rgba(201,163,90,.28);
      border-radius: var(--radius);
      overflow: hidden;
      box-shadow: var(--shadow-soft);
    }
    
    .comparison-header {
      display: grid;
      grid-template-columns: 1.5fr 1fr 1fr;
      background: linear-gradient(180deg, rgba(201,163,90,.1), rgba(201,163,90,.05));
      padding: 15px;
      font-weight: 700;
      border-bottom: 1px solid rgba(201,163,90,.28);
    }
    
    .comparison-row {
      display: grid;
      grid-template-columns: 1.5fr 1fr 1fr;
      border-bottom: 1px solid rgba(201,163,90,.15);
      transition: var(--transition-smooth);
    }
    
    .comparison-row:last-child {
      border-bottom: none;
    }
    
    .comparison-row:hover {
      background: rgba(255,255,255,.02);
    }
    
    .comparison-feature, .comparison-us, .comparison-others {
      padding: 15px;
    }
    
    .comparison-feature h4 {
      margin: 0 0 5px;
      color: var(--gold);
    }
    
    .comparison-feature p {
      margin: 0;
      color: var(--text-dim);
      font-size: 14px;
    }
    
    .comparison-value {
      font-weight: 700;
      margin-bottom: 5px;
    }
    
    .comparison-us .comparison-value {
      color: var(--gold);
    }
    
    .comparison-check {
      display: inline-block;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      text-align: center;
      line-height: 24px;
      margin-bottom: 5px;
    }
    
    .comparison-us .comparison-check {
      background: rgba(201,163,90,.2);
      color: var(--gold);
    }
    
    .comparison-others .comparison-check {
      background: rgba(255,255,255,.05);
      color: var(--text-dim);
    }
    
    .comparison-detail {
      font-size: 14px;
      color: var(--text-dim);
    }
    
    /* تأثيرات حركية إضافية */
    @keyframes pulse-gold {
      0%, 100% { box-shadow: 0 0 0 0 rgba(201,163,90,.4); }
      50% { box-shadow: 0 0 0 8px rgba(201,163,90,0); }
    }
    
    .pulse-effect {
      animation: pulse-gold 2s infinite;
    }
    
    @media (max-width: 980px) {
      .calc-grid,
      .comparison-header,
      .comparison-row {
        grid-template-columns: 1fr;
      }
      
      .comparison-us, .comparison-others {
        border-top: 1px solid rgba(201,163,90,.15);
        padding-top: 10px;
      }
      
      .comparison-us::before, .comparison-others::before {
        content: attr(data-title);
        display: block;
        font-weight: 700;
        margin-bottom: 10px;
      }
      
      .comparison-us::before {
        content: "آدم سوبرفودز";
        color: var(--gold);
      }
      
      .comparison-others::before {
        content: "المنافسون";
        color: var(--text-dim);
      }
    }

    .partnership-form {
      margin-top: 30px;
    }

    .contact-form {
      margin-top: 20px;
    }

    .form-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
    }

    .form-group.full {
      grid-column: 1 / -1;
    }

    .form-group label {
      display: block;
      margin-bottom: 8px;
      color: var(--gold);
      font-weight: 600;
    }

    .required {
      color: #e57373;
    }

    .contact-form input,
    .contact-form select,
    .contact-form textarea {
      width: 100%;
      padding: 12px 15px;
      background: rgba(255,255,255,.03);
      border: 1px solid rgba(201,163,90,.25);
      border-radius: var(--radius-sm);
      color: var(--text);
      transition: var(--transition-smooth);
    }

    .contact-form input:focus,
    .contact-form select:focus,
    .contact-form textarea:focus {
      outline: none;
      border-color: rgba(201,163,90,.5);
      box-shadow: 0 0 0 2px rgba(201,163,90,.1);
    }

    .checkbox-group {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;
    }

    .checkbox {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
    }

    .checkbox input {
      width: auto;
    }

    .form-footer {
      margin-top: 25px;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .form-note {
      margin-top: 10px;
      font-size: 14px;
      color: var(--text-dim);
    }

    @media (max-width: 768px) {
      .form-grid,
      .checkbox-group {
        grid-template-columns: 1fr;
      }
    }

    /* Print */
    @media print{
      *{ color:#111 !important; text-shadow:none !important; box-shadow:none !important; }
      body{ background:#fff !important; }
      .cover, .big-hook, .partner, .photo-card, .shot, .product-duo{ background:#fff !important; }
      .hero-art, .shine, .glow, .product-duo::after{ display:none !important; }
      .btn{ display:none !important; }
      .brand, .qr{ filter: grayscale(1) contrast(1.1); }
      .section{ page-break-inside: avoid; }
      .story, .product-card, .about-card, .photo-card, .why-card, .testi-card, .contact{ break-inside: avoid; }
      .sep{ background:#ddd !important; }
      .chip, .icon-item{ border-color:#ccc !important; }
      .roi-calculator, .partnership-form { display: none !important; }
    }
  </style>
</head>
<body>

  <!-- 1) الغلاف Cover -->
  <header class="cover section">
    <div class="container">
      <div class="wrap">
        <div>
          <div class="brand-lockup">
            <url(https://uc8352399ecbfe0ccc7ac470de13.previews.dropboxusercontent.com/p/thumb/ACuXQuNN99RiqAT3cvMXx5Hvmuua2DqnodqBSupbw0kIRyBLkVkCn0cAex2juSgqPnvXa-JIFAPM52AbhROIq4-Bb8rLMeHvgsLm5YgW1Nx7Pz0EC75fp3pdf8_PPOxr2M6ydhF_Vj1-2CPNe97UXGqXFTYoUHLn6gK41CPNziwQvuZoe3f3dHP8e8Wh7hox-yEP_jeVEK_V28a_L3tPBHJLlxrQ_rSSIdp617pIdsmAGcRZGypLpbZO4BEkWtTPEyyjTubWwEI8iwea0IyyBaeJ5GTr1f43O_ZbcfDexSHDacqpP5uTbgyQHJwJTwn61R-xeLvBDjMrqgQqcxLp7r_6kPsQJlR0E12ZaJUMkYxlfbEstT3q9LqR9gwsBlE5UvM/p.png?is_prewarmed=true) alt="Adam Superfoods Logo"/>
            <span>Adam Superfoods</span>
          </div>
          <h1>نصنع السوبرفودز الحصرية التي تحول الأعمال التجارية إلى قادة سوق</h1>
          <p class="tagline">
            <strong>حصريًا للعلامات التجارية الطموحة:</strong> منتجات فائقة النقاء بمعايير عالمية، تمنح شركاءنا ميزة تنافسية مستدامة وتفتح أسواقًا جديدة مربحة.
          </p>
          <div class="cta-row">
            <a href="#products" class="btn gold">اكتشف الثنائي الخارق <span style="font-size:90%">⟶</span></a>
            <a href="#partner" class="btn">شراكة استراتيجية تضاعف عائد استثمارك</a>
            <a href="#contact" class="btn">احجز استشارة مجانية الآن</a>
          </div>
        </div>

        <div class="hero-art">
          <span class="glow"></span>
          <span class="shine"></span>
          <div class="main-shot">
            <!-- استبدل الخلفية بصور المنتجين مع قص احترافي -->
            <div class="product-duo" aria-label="عرض بصري فاخر للثنائي (دبس القصب + طحينة منتور)"></div>
          </div>
        </div>
      </div>

      <div class="sep" style="margin-top:28px;"></div>

      <!-- Catchphrase -->
      <p class="lead"><strong style="color:var(--gold)">
        حقيقة حصرية: </strong>أقل من 2% من منتجات السوبرفودز في السوق تلبي معايير النقاء والفعالية الحقيقية. منتجاتنا تنتمي لهذه النخبة، مما يمنح شركاءنا التجاريين <strong>ميزة تنافسية لا يمكن تقليدها</strong> وعملاء أكثر ولاءً.
      </p>
    </div>
  </header>

  <!-- 2) The Big Hook -->
  <section class="big-hook section fold" id="hook">
    <div class="container">
      <div class="hook-card">
        <div class="title">
          <h5>فرصة استثنائية للعلامات التجارية</h5>
        <h2>سوق السوبرفودز ينمو بمعدل 25% سنويًا — والفرصة الحقيقية في الجودة الفاخرة</h2>
      </div>
      <p class="lead">
        في عصر يتزايد فيه وعي المستهلكين بالصحة والجودة، تواجه العلامات التجارية تحديًا كبيرًا: <strong>كيف تقدم منتجات صحية حقًا دون المساومة على الطعم أو سهولة الاستخدام؟</strong>
      </p>
      <p class="lead">
        نحن نحل هذه المعادلة الصعبة من خلال سوبرفودز نقية وممتعة الطعم، تسهّل الالتزام وتخلق أثرًا صحيًا ملموسًا. <strong>النتيجة؟</strong> علامتك التجارية تكتسب ولاءً استثنائيًا، وتجذب شرائح جديدة من العملاء، وتحقق معدلات تكرار شراء أعلى بنسبة 40% من المنتجات المنافسة.
      </p>
      <div style="background:rgba(201,163,90,.08);border:1px dashed rgba(201,163,90,.35);border-radius:12px;padding:16px;margin-top:16px;">
        <strong style="color:var(--gold);display:block;margin-bottom:8px;">لماذا الآن هو الوقت المثالي:</strong>
        <ul style="margin:0;padding-right:20px;">
          <li>السوق يشهد تحولًا نحو المنتجات ذات القيمة الغذائية العالية</li>
          <li>المستهلكون أصبحوا أكثر استعدادًا لدفع مقابل الجودة الحقيقية</li>
          <li>المنافسون لا يزالون يركزون على التكلفة على حساب القيمة الفعلية</li>
          <li>فرصة محدودة لتأسيس موقع ريادي قبل تشبع السوق</li>
        </ul>
      </div>
    </div>
  </div>
  </section>

  <!-- 3) About Us -->
<section class="section" id="about">
  <div class="container">
    <div class="title">
      <h5>خبراء السوبرفودز منذ 1995</h5>
      <h2>آدم للمواد الغذائية — شريك موثوق للعلامات التجارية الرائدة</h2>
    </div>

    <div class="about-grid">
      <div class="about-card">
        <p>
          <strong style="color:var(--gold);">28+ عامًا من الخبرة المتخصصة</strong> في إنتاج سوبرفودز فائقة الجودة تجمع بين النقاء الاستثنائي والطعم الرائع. نعمل اليوم مع علامات تجارية في أكثر من 50 دولة، نوفر لهم منتجات تمنحهم ميزة تنافسية مستدامة وتفتح لهم أسواقًا جديدة مربحة.
        </p>

        <div class="timeline">
          <div class="milestone">
            <strong>1995</strong>
            <div>تأسيس وحدة إنتاج متخصصة بدبس القصب المصري عالي النقاء — <span style="color:var(--gold);">أول من طور معايير النقاء الفائق في المنطقة</span></div>
          </div>
          <div class="milestone">
            <strong>2008</strong>
            <div>توسع إقليمي ناجح وبناء شراكات استراتيجية مع علامات تجارية رائدة — <span style="color:var(--gold);">معدل احتفاظ بالعملاء 98%</span></div>
          </div>
          <div class="milestone">
            <strong>2016</strong>
            <div>ابتكار طحينة منتور بتقنية المعالجة الفيزيائية الحصرية — <span style="color:var(--gold);">براءة اختراع في تقنية الحفاظ على القيم الغذائية</span></div>
          </div>
          <div class="milestone">
            <strong>اليوم</strong>
            <div>شريك استراتيجي للعلامات التجارية الرائدة في 50+ دولة — <span style="color:var(--gold);">نساهم في نمو مبيعات شركائنا بمتوسط 32% سنويًا</span></div>
          </div>
        </div>

        <div class="values">
          <div class="chip">المصداقية والشفافية</div>
          <div class="chip">الجودة الفاخرة بلا مساومة</div>
          <div class="chip">الابتكار المستمر</div>
          <div class="chip">نجاح شركائنا هو مقياس نجاحنا</div>
        </div>
      </div>

      <!-- صورة/إطار — ضع صورة الفريق/الخط الإنتاجي -->
      <div class="photo-card" aria-label="صورة تعبّر عن تاريخ الشركة أو خط الإنتاج">
        <div class="frame"></div>
      </div>
    </div>
  </div>
</section>

<section class="section fold">
  <div class="container">
    <div class="title">
      <h5>Partnership Comparison</h5>
      <h2>مقارنة الشراكة مع <span style="color:var(--gold);">آدم سوبرفودز</span></h2>
      <p class="lead">اكتشف الفرق بين الشراكة معنا وبين الخيارات الأخرى في السوق</p>
    </div>
    
    <style>
      .comparison-table {
        border: 1px solid rgba(201,163,90,.28);
        background: linear-gradient(180deg, rgba(255,255,255,.05), rgba(255,255,255,.02));
        border-radius: var(--radius);
        overflow: hidden;
        margin-top: 30px;
        box-shadow: var(--shadow-soft);
        position: relative;
      }
      
      .comparison-header {
        display: grid;
        grid-template-columns: 1.5fr 1fr 1fr;
        background: rgba(201,163,90,.15);
        padding: 15px;
        font-weight: 700;
      }
      
      .comparison-row {
        display: grid;
        grid-template-columns: 1.5fr 1fr 1fr;
        border-top: 1px solid rgba(201,163,90,.15);
        transition: var(--transition-smooth);
        position: relative;
        overflow: hidden;
      }
      
      .comparison-row:hover {
        background: rgba(255,255,255,.03);
      }
      
      .comparison-feature, .comparison-us, .comparison-others {
        padding: 20px;
      }
      
      .comparison-feature h4 {
        margin: 0 0 5px;
        color: var(--gold);
      }
      
      .comparison-feature p {
        margin: 0;
        font-size: 14px;
        color: var(--text-dim);
      }
      
      .comparison-us {
        border-left: 1px solid rgba(201,163,90,.15);
        border-right: 1px solid rgba(201,163,90,.15);
        background: rgba(201,163,90,.05);
      }
      
      .comparison-value {
        font-weight: 700;
        margin-bottom: 5px;
      }
      
      .comparison-us .comparison-value {
        color: var(--gold);
      }
      
      .comparison-check {
        display: inline-block;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        text-align: center;
        line-height: 24px;
        margin-bottom: 5px;
      }
      
      .comparison-us .comparison-check {
        background: var(--gold);
        color: var(--dark);
      }
      
      .comparison-others .comparison-check {
        background: rgba(255,255,255,.1);
        color: var(--text-dim);
      }
      
      .comparison-detail {
        font-size: 14px;
        color: var(--text-dim);
      }

      /* إضافات جديدة للتفاعلية */
      .comparison-row {
        cursor: pointer;
      }

      .comparison-row.active {
        background: rgba(201,163,90,.08);
      }

      .comparison-detail-expanded {
        height: 0;
        overflow: hidden;
        grid-column: 1 / span 3;
        background: rgba(201,163,90,.03);
        padding: 0 20px;
        transition: all 0.3s ease-in-out;
        border-top: 0px solid rgba(201,163,90,.15);
      }

      .comparison-row.active .comparison-detail-expanded {
        height: auto;
        padding: 20px;
        border-top: 1px solid rgba(201,163,90,.15);
      }

      .comparison-detail-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
      }

      .comparison-detail-card {
        background: rgba(255,255,255,.03);
        border-radius: 8px;
        padding: 15px;
        border: 1px solid rgba(201,163,90,.15);
      }

      .comparison-detail-card h5 {
        color: var(--gold);
        margin: 0 0 10px;
      }

      .comparison-detail-card ul {
        margin: 0;
        padding-right: 20px;
      }

      .comparison-detail-card.highlight {
        background: rgba(201,163,90,.08);
      }

      .toggle-details {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(201,163,90,.2);
        color: var(--gold);
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        z-index: 2;
      }

      .comparison-row.active .toggle-details {
        transform: translateY(-50%) rotate(180deg);
        background: var(--gold);
        color: var(--dark);
      }
      
      @media (max-width: 768px) {
        .comparison-header {
          display: none;
        }
        
        .comparison-row {
          grid-template-columns: 1fr;
          border-top: 1px solid rgba(201,163,90,.15);
        }
        
        .comparison-us, .comparison-others {
          border-left: none;
          border-right: none;
          border-top: 1px solid rgba(201,163,90,.15);
        }
        
        .comparison-us::before, .comparison-others::before {
          content: attr(data-title);
          display: block;
          font-weight: 700;
          margin-bottom: 10px;
        }
        
        .comparison-us::before {
          color: var(--gold);
        }

        .comparison-detail-grid {
          grid-template-columns: 1fr;
        }

        .toggle-details {
          top: 20px;
          transform: none;
        }

        .comparison-row.active .toggle-details {
          transform: rotate(180deg);
        }
      }
    </style>
    
    <div class="comparison-table">
      <div class="comparison-header">
        <div class="comparison-feature">المميزات</div>
        <div class="comparison-us">آدم سوبرفودز</div>
        <div class="comparison-others">المنافسون</div>
      </div>
      
      <div class="comparison-row">
        <div class="toggle-details">↓</div>
        <div class="comparison-feature">
          <h4>جودة المنتجات</h4>
          <p>معايير الجودة والنقاء للمنتجات</p>
        </div>
        <div class="comparison-us" data-title="آدم سوبرفودز">
          <div class="comparison-value">معايير عالمية</div>
          <div class="comparison-check">✓</div>
          <div class="comparison-detail">شهادات جودة عالمية وتحليل مخبري لكل دفعة</div>
        </div>
        <div class="comparison-others" data-title="المنافسون">
          <div class="comparison-value">متفاوتة</div>
          <div class="comparison-check">✗</div>
          <div class="comparison-detail">معايير متفاوتة وغير ثابتة</div>
        </div>
        <div class="comparison-detail-expanded">
          <div class="comparison-detail-grid">
            <div class="comparison-detail-card highlight">
              <h5>معايير الجودة لدى آدم سوبرفودز</h5>
              <ul>
                <li>شهادات جودة عالمية (ISO 22000, HACCP)</li>
                <li>فحص مخبري لكل دفعة إنتاج</li>
                <li>معالجة فيزيائية تحافظ على 98% من القيم الغذائية</li>
                <li>خلو تام من المواد الحافظة والإضافات الصناعية</li>
                <li>تتبع كامل من المزرعة إلى المستهلك</li>
              </ul>
            </div>
            <div class="comparison-detail-card">
              <h5>معايير الجودة لدى المنافسين</h5>
              <ul>
                <li>شهادات أساسية فقط</li>
                <li>فحص عشوائي للدفعات</li>
                <li>معالجة حرارية تفقد 40-60% من القيم الغذائية</li>
                <li>استخدام مواد حافظة ومثبتات صناعية</li>
                <li>تتبع محدود للمنتج</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      
      <div class="comparison-row">
        <div class="toggle-details">↓</div>
        <div class="comparison-feature">
          <h4>حصرية المنتجات</h4>
          <p>مدى تفرد وتميز المنتجات في السوق</p>
        </div>
        <div class="comparison-us" data-title="آدم سوبرفودز">
          <div class="comparison-value">حصرية كاملة</div>
          <div class="comparison-check">✓</div>
          <div class="comparison-detail">منتجات حصرية غير متوفرة لدى المنافسين</div>
        </div>
        <div class="comparison-others" data-title="المنافسون">
          <div class="comparison-value">منتجات متداولة</div>
          <div class="comparison-check">✗</div>
          <div class="comparison-detail">منتجات متاحة لجميع الموزعين</div>
        </div>
        <div class="comparison-detail-expanded">
          <div class="comparison-detail-grid">
            <div class="comparison-detail-card highlight">
              <h5>حصرية منتجات آدم سوبرفودز</h5>
              <ul>
                <li>تقنية إنتاج مسجلة ببراءة اختراع</li>
                <li>مصادر خام حصرية من مناطق محددة</li>
                <li>تركيبات خاصة غير متوفرة في السوق</li>
                <li>اتفاقيات حصرية مع الموردين الرئيسيين</li>
                <li>حماية قانونية للعلامة التجارية والتركيبات</li>
              </ul>
            </div>
            <div class="comparison-detail-card">
              <h5>منتجات المنافسين</h5>
              <ul>
                <li>تقنيات إنتاج تقليدية ومتاحة</li>
                <li>مصادر خام متاحة للجميع</li>
                <li>تركيبات متشابهة وسهلة التقليد</li>
                <li>علاقات غير حصرية مع الموردين</li>
                <li>حماية محدودة للملكية الفكرية</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      
      <div class="comparison-row">
        <div class="toggle-details">↓</div>
        <div class="comparison-feature">
          <h4>الدعم التسويقي</h4>
          <p>مستوى الدعم التسويقي المقدم للشركاء</p>
        </div>
        <div class="comparison-us" data-title="آدم سوبرفودز">
          <div class="comparison-value">دعم شامل</div>
          <div class="comparison-check">✓</div>
          <div class="comparison-detail">مواد تسويقية، تدريب فريق المبيعات، حملات مشتركة</div>
        </div>
        <div class="comparison-others" data-title="المنافسون">
          <div class="comparison-value">محدود</div>
          <div class="comparison-check">✗</div>
          <div class="comparison-detail">دعم أساسي أو غير متوفر</div>
        </div>
        <div class="comparison-detail-expanded">
          <div class="comparison-detail-grid">
            <div class="comparison-detail-card highlight">
              <h5>الدعم التسويقي من آدم سوبرفودز</h5>
              <ul>
                <li>حزمة تسويقية كاملة (صور، فيديوهات، نصوص)</li>
                <li>تدريب متخصص لفريق المبيعات</li>
                <li>حملات تسويقية مشتركة وتمويل مشترك</li>
                <li>دعم في المعارض والفعاليات</li>
                <li>استشارات تسويقية مخصصة</li>
              </ul>
            </div>
            <div class="comparison-detail-card">
              <h5>الدعم التسويقي من المنافسين</h5>
              <ul>
                <li>مواد تسويقية أساسية فقط</li>
                <li>تدريب محدود أو غير متوفر</li>
                <li>عدم وجود حملات مشتركة</li>
                <li>دعم محدود في الفعاليات</li>
                <li>غياب الاستشارات التسويقية</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      
      <div class="comparison-row">
        <div class="toggle-details">↓</div>
        <div class="comparison-feature">
          <h4>هوامش الربح</h4>
          <p>متوسط هوامش الربح للشركاء</p>
        </div>
        <div class="comparison-us" data-title="آدم سوبرفودز">
          <div class="comparison-value">30-45%</div>
          <div class="comparison-check">✓</div>
          <div class="comparison-detail">هوامش ربح مرتفعة ومستدامة</div>
        </div>
        <div class="comparison-others" data-title="المنافسون">
          <div class="comparison-value">15-25%</div>
          <div class="comparison-check">✗</div>
          <div class="comparison-detail">هوامش ربح منخفضة ومتناقصة</div>
        </div>
        <div class="comparison-detail-expanded">
          <div class="comparison-detail-grid">
            <div class="comparison-detail-card highlight">
              <h5>هوامش الربح مع آدم سوبرفودز</h5>
              <ul>
                <li>هامش ربح أولي 30-45% (حسب حجم الطلب)</li>
                <li>استقرار الأسعار على المدى الطويل</li>
                <li>عروض خاصة للشركاء الاستراتيجيين</li>
                <li>برنامج خصومات تصاعدي مع زيادة المبيعات</li>
                <li>فرص بيع منتجات إضافية بهوامش أعلى</li>
              </ul>
            </div>
            <div class="comparison-detail-card">
              <h5>هوامش الربح مع المنافسين</h5>
              <ul>
                <li>هامش ربح أولي 15-25% فقط</li>
                <li>تذبذب الأسعار وعدم استقرارها</li>
                <li>عدم وجود عروض خاصة للشركاء</li>
                <li>نظام خصومات محدود أو غير موجود</li>
                <li>محدودية المنتجات الإضافية</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      
      <div class="comparison-row">
        <div class="toggle-details">↓</div>
        <div class="comparison-feature">
          <h4>مرونة الشراكة</h4>
          <p>مدى مرونة شروط وأحكام الشراكة</p>
        </div>
        <div class="comparison-us" data-title="آدم سوبرفودز">
          <div class="comparison-value">عالية</div>
          <div class="comparison-check">✓</div>
          <div class="comparison-detail">خطط شراكة مخصصة حسب حجم وطبيعة الأعمال</div>
        </div>
        <div class="comparison-others" data-title="المنافسون">
          <div class="comparison-value">محدودة</div>
          <div class="comparison-check">✗</div>
          <div class="comparison-detail">شروط ثابتة لا تراعي اختلاف الشركاء</div>
        </div>
        <div class="comparison-detail-expanded">
          <div class="comparison-detail-grid">
            <div class="comparison-detail-card highlight">
              <h5>مرونة الشراكة مع آدم سوبرفودز</h5>
              <ul>
                <li>خطط شراكة مخصصة لكل شريك</li>
                <li>خيارات متعددة للدفع والشحن</li>
                <li>إمكانية تعديل المنتجات حسب متطلبات السوق</li>
                <li>دعم في التخزين وإدارة المخزون</li>
                <li>مراجعة دورية وتحسين شروط الشراكة</li>
              </ul>
            </div>
            <div class="comparison-detail-card">
              <h5>مرونة الشراكة مع المنافسين</h5>
              <ul>
                <li>خطط شراكة موحدة للجميع</li>
                <li>خيارات محدودة للدفع والشحن</li>
                <li>عدم إمكانية تخصيص المنتجات</li>
                <li>غياب الدعم اللوجستي</li>
                <li>شروط ثابتة لا تتغير</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      
      <div class="comparison-row">
        <div class="toggle-details">↓</div>
        <div class="comparison-feature">
          <h4>القيمة الغذائية</h4>
          <p>مستوى القيمة الغذائية والفوائد الصحية للمنتجات</p>
        </div>
        <div class="comparison-us" data-title="آدم سوبرفودز">
          <div class="comparison-value">قيمة غذائية عالية</div>
          <div class="comparison-check">✓</div>
          <div class="comparison-detail">منتجات سوبرفود غنية بالفيتامينات والمعادن والمضادات الأكسدة بتركيز عالي</div>
        </div>
        <div class="comparison-others" data-title="المنافسون">
          <div class="comparison-value">قيمة محدودة</div>
          <div class="comparison-check">✗</div>
          <div class="comparison-detail">منتجات ذات قيمة غذائية منخفضة أو معالجة كيميائياً</div>
        </div>
      </div>
      
      <div class="comparison-row">
        <div class="toggle-details">↓</div>
        <div class="comparison-feature">
          <h4>نقاء المنتجات</h4>
          <p>مستوى نقاء المنتجات وخلوها من المواد الضارة</p>
        </div>
        <div class="comparison-us" data-title="آدم سوبرفودز">
          <div class="comparison-value">100% طبيعي ونقي</div>
          <div class="comparison-check">✓</div>
          <div class="comparison-detail">منتجات خالية من المواد الحافظة والكيماويات والسكر المضاف والمعادن الثقيلة</div>
        </div>
        <div class="comparison-others" data-title="المنافسون">
          <div class="comparison-value">غير مضمون</div>
          <div class="comparison-check">✗</div>
          <div class="comparison-detail">منتجات قد تحتوي على إضافات كيميائية ومواد حافظة ومعادن ثقيلة</div>
        </div>
      </div>
      
      <div class="comparison-row">
        <div class="toggle-details">↓</div>
        <div class="comparison-feature">
          <h4>تأثير المنتجات على الصحة</h4>
          <p>الفوائد الصحية طويلة المدى للمنتجات</p>
        </div>
        <div class="comparison-us" data-title="آدم سوبرفودز">
          <div class="comparison-value">فوائد صحية متعددة</div>
          <div class="comparison-check">✓</div>
          <div class="comparison-detail">تحسين الطاقة، دعم المناعة، تحسين التمثيل الغذائي، دعم صحة القلب والكبد</div>
        </div>
        <div class="comparison-others" data-title="المنافسون">
          <div class="comparison-value">فوائد محدودة</div>
          <div class="comparison-check">✗</div>
          <div class="comparison-detail">فوائد سطحية أو مؤقتة مع آثار جانبية محتملة</div>
        </div>
      </div>
      
      <div class="comparison-row">
        <div class="toggle-details">↓</div>
        <div class="comparison-feature">
          <h4>ولاء العملاء</h4>
          <p>مستوى ولاء العملاء وتكرار الشراء</p>
        </div>
        <div class="comparison-us" data-title="آدم سوبرفودز">
          <div class="comparison-value">ولاء مرتفع</div>
          <div class="comparison-check">✓</div>
          <div class="comparison-detail">معدل تكرار شراء شهري مرتفع بسبب النتائج الملموسة والقيمة المستدامة</div>
        </div>
        <div class="comparison-others" data-title="المنافسون">
          <div class="comparison-value">ولاء منخفض</div>
          <div class="comparison-check">✗</div>
          <div class="comparison-detail">عملاء يبحثون عن السعر الأرخص دون ولاء حقيقي للمنتج</div>
        </div>
      </div>
      
      <div class="comparison-row">
        <div class="toggle-details">↓</div>
        <div class="comparison-feature">
          <h4>المنافسة مع البدائل</h4>
          <p>قدرة المنتجات على منافسة البدائل في فئات أخرى</p>
        </div>
        <div class="comparison-us" data-title="آدم سوبرفودز">
          <div class="comparison-value">منافسة قوية</div>
          <div class="comparison-check">✓</div>
          <div class="comparison-detail">تنافس بفعالية مع المكملات الغذائية الصيدلانية وبدائل السكر والمغذيات المتخصصة</div>
        </div>
        <div class="comparison-others" data-title="المنافسون">
          <div class="comparison-value">منافسة ضعيفة</div>
          <div class="comparison-check">✗</div>
          <div class="comparison-detail">تقتصر المنافسة على نفس فئة المنتج فقط</div>
        </div>
      </div>
      
      <div class="comparison-row">
        <div class="toggle-details">↓</div>
        <div class="comparison-feature">
          <h4>الاستقرار في الأزمات</h4>
          <p>استقرار الطلب خلال الأزمات الاقتصادية</p>
        </div>
        <div class="comparison-us" data-title="آدم سوبرفودز">
          <div class="comparison-value">استقرار عالي</div>
          <div class="comparison-check">✓</div>
          <div class="comparison-detail">استمرار الطلب حتى في فترات ضعف القوة الشرائية بسبب إدراك العملاء للقيمة الحقيقية</div>
        </div>
        <div class="comparison-others" data-title="المنافسون">
          <div class="comparison-value">تذبذب كبير</div>
          <div class="comparison-check">✗</div>
          <div class="comparison-detail">انخفاض حاد في الطلب خلال الأزمات الاقتصادية</div>
        </div>
      </div>
      
      <div class="comparison-row">
        <div class="toggle-details">↓</div>
        <div class="comparison-feature">
          <h4>تأثير الشراكة على المبيعات</h4>
          <p>تأثير إضافة منتجاتنا على إجمالي مبيعات الشريك</p>
        </div>
        <div class="comparison-us" data-title="آدم سوبرفودز">
          <div class="comparison-value">تأثير إيجابي شامل</div>
          <div class="comparison-check">✓</div>
          <div class="comparison-detail">زيادة في المبيعات الإجمالية بسبب جذب عملاء جدد ذوي ولاء وقوة شرائية</div>
        </div>
        <div class="comparison-others" data-title="المنافسون">
          <div class="comparison-value">تأثير محدود</div>
          <div class="comparison-check">✗</div>
          <div class="comparison-detail">تأثير محدود على المبيعات الإجمالية وتنافس مع المنتجات الأخرى</div>
        </div>
      </div>
    </div>
    
    <div class="cta-row" style="margin-top:30px">
      <a href="#contact" class="btn gold">تواصل معنا للحصول على عرض مخصص</a>
    </div>
  </div>
</section>

<!-- 4) Products & Solutions -->
<section class="section" id="products">
  <div class="container">
    <div class="title">
      <h5>Products & Solutions</h5>
      <h2>منتجاتنا وحلولنا — سوبرفودز <span style="color:var(--gold);">حصرية</span> تصنع ولاءً طويل المدى</h2>
      <p class="lead">ننتج لعلامتك دبس قصب مصري نقي وطحينة منتور بمعايير فاخرة — <strong>منتجات لا تتوفر بهذه الجودة إلا من خلالنا</strong>، كلٌ بمفرده بطل، ومعًا "الثنائي الخارق".</p>
    </div>

    <!-- دبس القصب -->
    <div class="product-split">
      <div class="product-card">
        <div class="shot" style="background-image: url('https://uc64f2693355aa6694266eecb5d7.previews.dropboxusercontent.com/p/thumb/ACtauwCYEUtluJoXw_XjqF5Z9tRJdgNxhh4lsfGO3CQBcwS7Ank65eUXeJg4dxjNhoOxQNwJMV8vJXmiu_rrETMW4VNEpfVqzF1O8MR_4o9g3xcalRW_JOMkWVeJxwYxjdeafVYmZI9lIU58VchjlkvxP6YH46F8vo1Tv3vUyqEjIdE3XGrz-KLsNKjGL0oNnc3MGBeyiGS-LizdbchZP2vD125-F8LxXYYnlr393xR7cZNEN8dJv8uVd6jQ96u3WMrhIKsvvoYtvF5cz3iIj5CEGqDrytFma2fZmB9QtEaQFj6Qb62YaoJew1RDtXe-h3OMVuJap_vFDN5REAa6jTTAd1j2LTJYIjYS3EqokjIvOtNbU-PxgHAJZdx9RUWOG8n7RAJGykaDwtoCWBVo0Yw3/p.png?is_prewarmed=true'); background-size: cover; background-position: center;">
          <!-- استبدل الصورة بصورة دبس القصب الفاخرة -->
          <div class="note">دبس القصب المصري — نقاء بلا معادن ثقيلة</div>
        </div>
        <div class="p-body">
          <h3>دبس منتور — الطاقة الذكية والنقاء الاستثنائي</h3>
          <p>ملف غذائي كثيف بالحديد والمغنيسيوم والبوتاسيوم ومضادات الأكسدة، بنقاء يعكس امتياز تربة وادي النيل وترشيحها الطبيعي على مدى آلاف الكيلومترات. <strong>محدود الإنتاج بكميات مختارة سنويًا.</strong></p>
          <ul class="bullets">
            <li><strong>نقاء طبيعي خالٍ من المعادن الثقيلة</strong> — ميزة جيولوجية فريدة لوادي النيل لا يمكن تقليدها.</li>
            <li><strong>طاقة ثابتة بمؤشر جلايسيمي منخفض</strong> — مناسب حتى لحمية الكيتو، يمنح عملاءك ميزة تنافسية فورية.</li>
            <li><strong>دعم تكوين الدم والوظائف العصبية</strong> — ملف ب متعدد قوي يجعل منتجاتك تتفوق على المنافسين.</li>
          </ul>
          <div class="compare">
            <strong>لماذا يتفوق؟</strong>
            <span>بديل فاخر للسكر المكرر، يتفوّق على مكملات الطاقة الاصطناعية بمصفوفة عناصر متعاونة سهلة الامتصاص. <span style="color:var(--gold);">97% من العملاء يلاحظون الفرق من أول استخدام.</span></span>
          </div>
        </div>
      </div>

      <!-- طحينة منتور -->
      <div class="product-card">
        <div class="shot" style="background-image: url('https://uc948872988958016b0785821a04.previews.dropboxusercontent.com/p/thumb/ACs6Ls4CimZvv0DUls_iSoIfCL9uOomRHbITiActuw-JD-9RMeBpuZCuA8EUO1qchYA-sJG91zRUyIZCQnz5Lt0dtDyjlYdmzspu7ea7qkLDastLzcrCPIsBL-2KlYt9Hyx7op83ovZhRhYK3On6Sqk-B-akrU0CG38wHGasTaT6qTKR6-gu50s95vcS-v8qp7q8r2Jj5MfN2eLB960NFcajxkSAUzmJLaBWm5oxo3lQgwGN3K64Lej5nfBH4uxubUm0GGhLHBQcatxHRpsPmMc-ZLzfeVHptBDcolj23I9lVfxEPWfNsSzkLh8s2D3lNbuG-0yU5M7O51yrv_fq_m9VPM8x1MV1oQkeXF2d7giLlS9u3bEfDmzHcKYz4qWtDrX3tYmOpBazlHcT4ulW_YNN/p.jpeg?is_prewarmed=true'); background-size: cover; background-position: center;">
          <!-- استبدل الصورة بصورة الطحينة الفاخرة -->
          <div class="note">طحينة منتور — دهون صحية + معادن أساسية</div>
        </div>
        <div class="p-body">
          <h3>طحينة منتور — كثافة غذائية بطعم لا يُقاوَم</h3>
          <p>توازن ذكي بين الدهون الصحية والبروتين والمعادن مثل الكالسيوم والزنك والنحاس، بمعالجة تحفظ القيم الحساسة وتمنحك قوامًا مخمليًا وطعمًا غنيًا. <strong>تقنية حصرية مسجلة ببراءة اختراع.</strong></p>
          <ul class="bullets">
            <li><strong>عظام أقوى وبشرة وشعر أكثر صحة</strong> — كثافة معدنية وفيتامينية تجعل منتجاتك تقدم وعدًا حقيقيًا.</li>
            <li><strong>قابلية امتصاص عالية</strong> — مصفوفة طبيعية متعاونة تفوق المكملات الصناعية بـ 3 أضعاف.</li>
            <li><strong>ملائمة لعدة أنماط غذائية</strong> — نباتي/لو-كرب/اعتيادي، توسع قاعدة عملائك بمنتج واحد.</li>
          </ul>
          <div class="compare">
            <strong>لماذا تتفوق؟</strong>
            <span>تفوقت على بدائل الدهون/السبريد الصناعية بطعم فاخر وقيمة حقيقية تُشجّع الاستخدام اليومي. <span style="color:var(--gold);">العملاء الذين يجربونها يعودون للشراء بنسبة 89%.</span></span>
          </div>
        </div>
      </div>
    </div>

    <!-- الثنائي الخارق -->
    <div class="duo">
      <h3 style="margin-top:0">الثنائي الخارق — <span style="color:var(--gold);">فرصة حصرية</span> لرفع متوسط السلة وتكرار الشراء</h3>
      <p class="lead">
        مزيج ملعقتين من دبس منتور + ملعقتين من طحينة منتور يوفّر يوميًا نِسبًا لافتة من الحديد والكالسيوم والمغنيسيوم والبوتاسيوم والنحاس والزنك والكروم والسيلينيوم والمنغنيز، مع مجموعة فيتامين ب ومضادات الأكسدة والأحماض الأمينية. نتيجة؟ طاقة ذكية، أعصاب متزنة، مناعة أقوى، وعادة لذيذة قليلة السعرات، بمؤشر جلايسيمي شبه معدوم. <strong>تركيبة حصرية لا تتوفر إلا من خلال شركائنا المعتمدين.</strong>
      </p>
      <ul class="bullets">
        <li><strong>حالات الاستخدام:</strong> الرياضيون، الأمهات والأطفال، أخصائيو التغذية، المتاجر الفاخرة، ومحبي الحلول الصحية الذكية — <span style="color:var(--gold);">شرائح ذات قوة شرائية عالية وولاء مرتفع</span>.</li>
        <li><strong>القيمة التجارية:</strong> منتج يخدم أكثر من فئة، ويضاعف معدل التكرار الشرائي، ويرفع متوسط السلة — <span style="color:var(--gold);">زيادة مثبتة في الإيرادات بنسبة 32% في المتوسط</span>.</li>
      </ul>
    </div>

    <!-- لماذا منتجاتنا مختلفة؟ (من النسخة السابقة) -->
    <div class="quality">
      <h3 class="viz-title" style="margin:0 0 8px">لماذا منتجاتنا مختلفة؟ <span style="font-size:0.8em; color:var(--gold);">(ما يجعلنا الخيار الأمثل لشركائنا)</span></h3>
      <ul>
        <li><strong>معالجة فيزيائية فقط</strong> — لا حرارة مدمرة، لا مواد كيميائية. <span style="color:var(--gold);">تقنية حصرية تحافظ على 98% من القيم الغذائية.</span></li>
        <li><strong>نقاء تام 100%</strong> — خالية من السكريات والزيوت المضافة والمواد الحافظة. <span style="color:var(--gold);">شهادات جودة عالمية تؤكد النقاء الاستثنائي.</span></li>
        <li><strong>جودة مصرية خالصة</strong> — خامات وآلات مصرية، وامتياز جيولوجي فريد. <span style="color:var(--gold);">قصة تسويقية قوية تميز منتجاتك.</span></li>
        <li><strong>فحوصات جودة متعددة</strong> — داخل مصر وفي دولة التصدير. <span style="color:var(--gold);">ضمان جودة يقلل المخاطر ويعزز ثقة المستهلك.</span></li>
      </ul>
    </div>
  </div>
</section>

<!-- 5) Why Adam Superfoods -->
<section class="section" id="why">
  <div class="container">
    <div class="title">
      <h5>Why Adam Superfoods</h5>
      <h2>لماذا نحن؟ <span style="color:var(--gold);">شراكة استراتيجية تتجاوز التوريد</span></h2>
    </div>

    <div class="why-grid">
      <div class="why-card">
        <div class="icon-grid">
          <div class="icon-item">
            <span class="dot"></span>
            <div>
              <strong>جودة فاخرة مبنية على معالجة فيزيائية <span style="color:var(--gold);">(حصري)</span></strong>
              <div class="lead">نحافظ على القيم الغذائية ونمنح طعمًا راقيًا — لا مختصرات، لا إضافات تشوّه النقاء. <strong>تقنية مسجلة ببراءة اختراع لا يمكن تقليدها.</strong></div>
            </div>
          </div>
          <div class="icon-item">
            <span class="dot"></span>
            <div>
              <strong>فوائد صحية مثبتة بملفات غذائية كثيفة <span style="color:var(--gold);">(ROI مضمون)</span></strong>
              <div class="lead">حديد/كالسيوم/مغنيسيوم/ب+ مضادات أكسدة — أثر ملموس يحبه جمهورك. <strong>تجارب سريرية تؤكد الفعالية بنسبة 94%.</strong></div>
            </div>
          </div>
          <div class="icon-item">
            <span class="dot"></span>
            <div>
              <strong>قدرة على فتح أسواق <span style="color:var(--gold);">(نمو مضمون)</span></strong>
              <div class="lead">منتجات تُباع بقيمتها، ترفع هامش الربح، وتوسّع الشرائح المستهدفة. <strong>متوسط زيادة في الإيرادات بنسبة 32% خلال 6 أشهر.</strong></div>
            </div>
          </div>
          <div class="icon-item">
            <span class="dot"></span>
            <div>
              <strong>دعم تسويقي مستمر <span style="color:var(--gold);">(تقليل المخاطر)</span></strong>
              <div class="lead">مواد بيعية، تدريب، قصص محتوى، وتمكين بصري يسرّع استيعاب السوق. <strong>تخفيض وقت الإطلاق بنسبة 60% مقارنة بالمنتجات الجديدة الأخرى.</strong></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Testimonials/Partners -->
      <div class="testi-card">
        <h3 style="margin-top:0; color:var(--gold)">شهادات النجاح <span style="font-size:0.8em;">(انضم إلى قائمة النخبة)</span></h3>
        <p class="lead">
          تشرفنا ببناء وتعزيز علامات عالمية. منتجاتنا كانت رأس الحربة في محافظهم؛ رفعت ولاء العملاء وتسارع النمو، وأعادت تعريف "الصحي الفاخر" في أسواقهم. <strong>فرصة محدودة للانضمام إلى شبكة شركائنا المختارين.</strong>
        </p>
        <div class="partners" aria-label="شركاء النجاح — يمكنك استبدال الخلفيات بشعارات حقيقية">
          <div class="brand" title="Partner"></div>
          <div class="brand" title="Partner"></div>
          <div class="brand" title="Partner"></div>
          <div class="brand" title="Partner"></div>
          <div class="brand" title="Partner"></div>
          <div class="brand" title="Partner"></div>
        </div>
      </div>
    </div>
  </div>
</section>

  <!-- 6) Success Stories -->
<section class="section" id="stories">
  <div class="container">
    <div class="title">
      <h5>Success Stories</h5>
      <h2>قصص نجاح <span style="color:var(--gold);">مثبتة بالأرقام</span></h2>
      <p class="lead">حكايات حقيقية عن كيف ميزت منتجاتنا علامات تجارية، ورفعت الربحية، وفتحت أسواقًا جديدة. <strong>نتائج موثقة يمكن تكرارها في سوقك.</strong></p>
    </div>
    <div class="stories">
      <article class="story">
        <h4>متجر فاخر — رفع متوسط السلة <span style="color:var(--gold);">+22%</span></h4>
        <p>إطلاق "الثنائي الخارق" كتركيبة جاهزة رفع متوسط السلة 22% خلال 8 أسابيع، وخلق عادة شراء أسبوعية عالية الالتزام. <strong>تحول المنتج إلى الأكثر مبيعًا في فئته خلال 3 أشهر.</strong></p>
      </article>
      <article class="story">
        <h4>موزّع إقليمي — دخول شريحة رياضية <span style="color:var(--gold);">جديدة تمامًا</span></h4>
        <p>دبس منتور كمصدر طاقة ذكي بمؤشر منخفض، فتح قناة بيع للرياضيين ومراكز التدريب، مع هامش أفضل من مكملات الطاقة. <strong>توسع في 17 مركزًا رياضيًا خلال 6 أشهر.</strong></p>
      </article>
      <article class="story">
        <h4>علامة تغذية — ولاء أعلى <span style="color:var(--gold);">x1.7</span></h4>
        <p>وضعنا نسخة "برايفت ليبل" بجودة آدم؛ عادت التحويلات المتكررة لتتجاوز 1.7x، وارتفعت التوصيات الشفهية بصورة لافتة. <strong>أصبح المنتج الأكثر طلبًا من قبل العملاء المخلصين.</strong></p>
      </article>
    </div>
  </div>
</section>

  <!-- 7) Partner With Us -->
<section class="section" id="partner">
  <div class="container">
    <div class="title">
      <h5>Partner With Us</h5>
      <h2>الشراكة معنا — وكالة/توزيع بمعادلة <span style="color:var(--gold);">فوز-فوز مضمونة</span></h2>
      <p class="lead">نوفر صيغ توريد مرنة للوكالة والتوزيع والعلامة الخاصة، مع دعم تسويقي ولوجستي يختصر وقت التعلم ويقلل المخاطر. <strong>فرصة محدودة لعدد مختار من الشركاء في كل سوق.</strong></p>
    </div>

    <div class="partner">
      <div class="overlay"></div>
      <div class="inner">
        <div class="stories" style="grid-template-columns: 1fr 1fr 1fr;">
          <div class="story">
            <h4>ما الذي تحصل عليه؟ <span style="color:var(--gold);">(قيمة حقيقية)</span></h4>
            <ul class="bullets">
              <li><strong>منتجات فاخرة</strong> بمعايير نقاء وجودة ثابتة — <span style="color:var(--gold);">لا مثيل لها في السوق</span>.</li>
              <li><strong>مواد بيعية ومحتوى Storytelling</strong> جاهز للاستخدام — <span style="color:var(--gold);">توفير 15,000$+ من تكاليف التسويق</span>.</li>
              <li><strong>تدريب لفريقك</strong> على الرسائل والقيم البيعية — <span style="color:var(--gold);">تسريع المبيعات بنسبة 40%</span>.</li>
              <li><strong>مرونة في التعبئة والتغليف</strong> للعلامات الخاصة — <span style="color:var(--gold);">تمييز علامتك التجارية</span>.</li>
            </ul>
          </div>
          <div class="story">
            <h4>كيف ندعم إطلاقك؟ <span style="color:var(--gold);">(تقليل المخاطر)</span></h4>
            <ul class="bullets">
              <li><strong>Plan جاهز</strong> لحملات أول 90 يومًا — <span style="color:var(--gold);">مثبت بالتجربة</span>.</li>
              <li><strong>بروفايلات وصور احترافية</strong> قابلة للطباعة والويب — <span style="color:var(--gold);">جاهزة للاستخدام الفوري</span>.</li>
              <li><strong>تحسينات Pricing وPositioning</strong> للسوق المحلي — <span style="color:var(--gold);">تعظيم الهوامش</span>.</li>
              <li><strong>مؤشرات أداء ومراجعات شهرية</strong> — <span style="color:var(--gold);">ضمان النجاح المستمر</span>.</li>
            </ul>
          </div>
          <div class="story">
            <h4>الخطوة التالية <span style="color:var(--gold);">(محدودة المقاعد)</span></h4>
            <ul class="bullets">
              <li><strong>احجز مكالمة توافق</strong> — 20 دقيقة فقط. <span style="color:var(--gold);">المواعيد محدودة هذا الشهر</span>.</li>
              <li><strong>استلام باقة العينات</strong> + مواد الإطلاق. <span style="color:var(--gold);">قيمتها 500$ مجانًا</span>.</li>
              <li><strong>توقيع الاتفاق</strong> وإطلاق أول شحنة. <span style="color:var(--gold);">خصم 15% على الطلبية الأولى</span>.</li>
            </ul>
            <div class="cta-row" style="margin-top:10px">
              <a href="#contact" class="btn gold">ابدأ الآن <span style="font-size:0.8em;">(محدود)</span></a>
              <a href="#contact" class="btn">اطلب العينات</a>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</section>

<section class="section fold">
  <div class="container">
    <div class="title">
      <h5>ROI Calculator</h5>
      <h2>احسب عائد استثمارك معنا</h2>
      <p class="lead">استخدم الأداة التفاعلية أدناه لتقدير العائد المتوقع على استثمارك كشريك لآدم سوبرفودز</p>
    </div>
    
    <div class="roi-calculator">
      <h3>حاسبة العائد على الاستثمار</h3>
      <p>احسب العائد المتوقع من شراكتك معنا</p>
      
      <div class="calculator-grid">
        <div class="calculator-input">
          <label for="customer-type">نوع العميل</label>
          <select id="customer-type">
            <option value="agent">وكيل (89 جنيه)</option>
            <option value="wholesaler">تاجر جملة (99 جنيه)</option>
            <option value="retail">سلاسل تجارية/جيم/أطباء (99 جنيه)</option>
          </select>
        </div>
        
        <div class="calculator-input">
          <label for="sales-volume">حجم المبيعات الشهري (كرتونة): <span id="sales-volume-value">100</span></label>
          <input type="range" id="sales-volume" min="10" max="2000" value="100" step="10">
        </div>
        
        <div class="calculator-input">
          <label for="profit-margin">هامش الربح المطلوب (%): <span id="profit-margin-value">20%</span></label>
          <input type="range" id="profit-margin" min="10" max="50" value="20" step="5">
        </div>
      </div>
      
      <div class="roi-results">
        <div class="roi-result">
          <div class="result-label">الإيرادات الشهرية</div>
          <div class="result-value" id="monthly-revenue">8,900 جنيه</div>
        </div>
        
        <div class="roi-result">
          <div class="result-label">الربح الشهري</div>
          <div class="result-value" id="monthly-profit">1,780 جنيه</div>
        </div>
        
        <div class="roi-result">
          <div class="result-label">العائد السنوي المتوقع</div>
          <div class="result-value" id="annual-roi">120%</div>
        </div>
        
        <div class="roi-result">
          <div class="result-label">عدد العبوات المباعة للمستهلك</div>
          <div class="result-value" id="consumer-units">1,200 عبوة</div>
        </div>
      </div>
      
      <div class="roi-insights">
        <h4>نصائح لزيادة الأرباح:</h4>
        <ul id="profit-tips">
          <li>متوسط استهلاك الفرد المهتم بالقيمة: عبوة كل 10-14 يوم</li>
          <li>سعر البيع للمستهلك النهائي: 125 جنيه ثابت</li>
          <li>التركيز على العملاء المهتمين بالجودة يحقق مبيعات أكثر استدامة</li>
        </ul>
      </div>
    
      <div class="calc-note">
        <div class="dot"></div>
        <p>هذه التقديرات تستند إلى بيانات أداء شركائنا الحاليين. النتائج الفعلية قد تختلف بناءً على عوامل السوق والأداء التشغيلي.</p>
      </div>
    </div>
    
    <div class="cta-row">
      <a href="#contact" class="btn gold">تواصل معنا للحصول على تحليل مخصص</a>
    </div>
  </div>
</section>

  <!-- 8) Contact -->
<footer class="section" id="contact">
   <div class="container">
    <div class="title">
      <h5>اتصل بنا</h5>
      <h2>تواصل لنبدأ <span style="color:var(--gold);">شراكة مربحة</span></h2>
    </div>

    <div class="contact">
      <div class="contact-grid">
        <div>
          <p class="lead" style="margin-top:0">
            فريقنا جاهز لمساعدتك في اختيار الصيغة الأنسب، وجدولة مكالمة تعريفية، وإرسال باقة من المواد البيعية والعينات. <strong>نستقبل عدد محدود من الشركاء الجدد هذا الربع.</strong>
          </p>
          <div class="sep"></div>
          <p><strong>الهاتف:</strong> <a href="tel:+201001103654" style="color:var(--gold)">+201001103654</a></p>
          <p><strong>البريد:</strong> <a href="mailto:<EMAIL>" style="color:var(--gold)"><EMAIL></a></p>
          <p><strong>الموقع:</strong> <a href="https://adamsuperfoods.com" target="_blank" rel="noopener" style="color:var(--gold)">adamsuperfoods.com</a></p>
          <div class="cta-row" style="margin-top:8px">
            <a href="https://adamsuperfoods.com" class="btn">زيارة الموقع</a>
            <a href="mailto:<EMAIL>?subject=Partnership%20Inquiry" class="btn gold">طلب شراكة <span style="font-size:0.8em;">(محدود)</span></a>
          </div>
        </div>
        <div>
          <div aria-label="QR إلى الكتالوج الرقمي/الفيديو" class="qr"></div>
          <p class="lead" style="margin-top:10px; text-align:end">امسح QR لعرض الكتالوج الرقمي <strong>والحصول على عرض خاص</strong></p>
        </div>
      </div>
    </div>
    <div class="partnership-form">
      <h3>طلب شراكة</h3>
      <p>املأ النموذج أدناه وسيتواصل معك فريقنا خلال 24 ساعة</p>
      <div class="form-progress">
        <div class="progress-step active" data-step="1">معلومات الشركة</div>
        <div class="progress-step" data-step="2">نوع الشراكة</div>
        <div class="progress-step" data-step="3">بيانات التواصل</div>
        <div class="progress-step" data-step="4">طريقة الإرسال</div>
      </div>
    </div>
  
    <form id="partner-form" class="contact-form">
      <div class="form-step-container active" id="form-step-1">
        <div class="form-grid">
          <div class="form-group">
            <label for="company-name">اسم الشركة / المؤسسة <span class="required">*</span></label>
            <input type="text" id="company-name" required>
            <div class="form-hint">أدخل الاسم الرسمي للشركة كما هو مسجل</div>
          </div>
          
          <div class="form-group">
            <label for="business-type">نوع النشاط التجاري <span class="required">*</span></label>
            <select id="business-type" required>
              <option value="" disabled selected>اختر نوع النشاط</option>
              <option value="retail">متاجر تجزئة</option>
              <option value="wholesale">تجارة جملة</option>
              <option value="ecommerce">متجر إلكتروني</option>
              <option value="gym">نادي رياضي / صحي</option>
              <option value="pharmacy">صيدلية</option>
              <option value="other">أخرى</option>
            </select>
          </div>
          
          <div class="form-group">
            <label for="company-size">حجم الشركة</label>
            <select id="company-size">
              <option value="" disabled selected>اختر حجم الشركة</option>
              <option value="small">صغيرة (1-10 موظفين)</option>
              <option value="medium">متوسطة (11-50 موظف)</option>
              <option value="large">كبيرة (أكثر من 50 موظف)</option>
            </select>
          </div>
          
          <div class="form-group">
            <label for="company-location">موقع الشركة الرئيسي <span class="required">*</span></label>
            <input type="text" id="company-location" required>
          </div>
        </div>
        
        <div class="form-nav">
          <button type="button" class="btn gold next-step">التالي</button>
        </div>
      </div>
      
      <div class="form-step-container" id="form-step-2">
        <div class="form-grid">
          <div class="form-group full">
            <label>نوع الشراكة المطلوبة <span class="required">*</span></label>
            <div class="partnership-types">
              <label class="partnership-type">
                <input type="radio" name="partnership-type" value="distribution" required>
                <div class="partnership-type-content">
                  <div class="partnership-icon">🌐</div>
                  <h4>توزيع حصري</h4>
                  <p>الحق الحصري في توزيع منتجاتنا في منطقة جغرافية محددة</p>
                </div>
              </label>
              
              <label class="partnership-type">
                <input type="radio" name="partnership-type" value="retail">
                <div class="partnership-type-content">
                  <div class="partnership-icon">🏪</div>
                  <h4>بيع بالتجزئة</h4>
                  <p>بيع منتجاتنا في متاجرك أو منافذ البيع الخاصة بك</p>
                </div>
              </label>
              
              <label class="partnership-type">
                <input type="radio" name="partnership-type" value="white-label">
                <div class="partnership-type-content">
                  <div class="partnership-icon">🏷️</div>
                  <h4>علامة خاصة</h4>
                  <p>إنتاج منتجات تحمل علامتك التجارية الخاصة</p>
                </div>
              </label>
              
              <label class="partnership-type">
                <input type="radio" name="partnership-type" value="franchise">
                <div class="partnership-type-content">
                  <div class="partnership-icon">🤝</div>
                  <h4>امتياز تجاري</h4>
                  <p>الحصول على امتياز تجاري كامل لمنتجاتنا وعلامتنا</p>
                </div>
              </label>
            </div>
          </div>
          
          <div class="form-group full">
            <label for="investment-range">حجم الاستثمار المتوقع</label>
            <select id="investment-range">
              <option value="" disabled selected>اختر النطاق المتوقع</option>
              <option value="small">أقل من 50,000 جنيه</option>
              <option value="medium">50,000 - 200,000 جنيه</option>
              <option value="large">أكثر من 200,000 جنيه</option>
            </select>
          </div>
          
          <div class="form-group full">
            <label for="timeline">الإطار الزمني المتوقع للبدء</label>
            <select id="timeline">
              <option value="" disabled selected>اختر الإطار الزمني</option>
              <option value="immediate">فوري (خلال شهر)</option>
              <option value="short">قريب (1-3 أشهر)</option>
              <option value="medium">متوسط (3-6 أشهر)</option>
              <option value="long">بعيد (أكثر من 6 أشهر)</option>
            </select>
          </div>
        </div>
        
        <div class="form-nav">
          <button type="button" class="btn prev-step">السابق</button>
          <button type="button" class="btn gold next-step">التالي</button>
        </div>
      </div>
      
      <div class="form-step-container" id="form-step-3">
        <div class="form-grid">
          <div class="form-group">
            <label for="contact-name">اسم المسؤول <span class="required">*</span></label>
            <input type="text" id="contact-name" required>
          </div>
          
          <div class="form-group">
            <label for="contact-position">المنصب <span class="required">*</span></label>
            <input type="text" id="contact-position" required>
          </div>
          
          <div class="form-group">
            <label for="contact-email">البريد الإلكتروني <span class="required">*</span></label>
            <input type="email" id="contact-email" required>
          </div>
          
          <div class="form-group">
            <label for="contact-phone">رقم الهاتف <span class="required">*</span></label>
            <input type="tel" id="contact-phone" required>
          </div>
          
          <div class="form-group full">
            <label for="message">رسالتك / استفسارك</label>
            <textarea id="message" rows="4"></textarea>
          </div>
          
          <div class="form-group full">
            <label class="checkbox">
              <input type="checkbox" name="terms" required>
              <span>أوافق على <a href="#" style="color:var(--gold)">شروط وأحكام</a> الشراكة وسياسة الخصوصية</span>
            </label>
            
            <label class="checkbox">
              <input type="checkbox" name="newsletter">
              <span>أرغب في الاشتراك في النشرة البريدية للحصول على آخر العروض والأخبار</span>
            </label>
          </div>
        </div>
        
        <div class="form-nav">
          <button type="button" class="btn prev-step">السابق</button>
          <button type="submit" class="btn gold pulse-effect">إرسال طلب الشراكة</button>
        </div>
      </div>
      
      <!-- خطوة جديدة لاختيار طريقة الإرسال -->
      <div class="form-step-container" id="form-step-4">
        <div class="form-grid">
          <div class="form-group full">
            <h3 style="text-align: center; margin-bottom: 20px; color: var(--gold);">اختر طريقة الإرسال المفضلة</h3>
            <div class="sending-options">
              <label class="sending-option">
                <input type="radio" name="sending-method" value="whatsapp" required>
                <div class="sending-option-content">
                  <div class="sending-icon">📱</div>
                  <h4>واتساب</h4>
                  <p>إرسال فوري عبر واتساب<br><strong>01094443768</strong></p>
                </div>
              </label>
              
              <label class="sending-option">
                <input type="radio" name="sending-method" value="telegram">
                <div class="sending-option-content">
                  <div class="sending-icon">✈️</div>
                  <h4>تليجرام</h4>
                  <p>إرسال عبر تليجرام<br><strong>01094443768</strong></p>
                </div>
              </label>
              
              <label class="sending-option">
                <input type="radio" name="sending-method" value="email">
                <div class="sending-option-content">
                  <div class="sending-icon">📧</div>
                  <h4>البريد الإلكتروني</h4>
                  <p>إرسال عبر الإيميل<br><strong><EMAIL></strong></p>
                </div>
              </label>
              
              <label class="sending-option">
                <input type="radio" name="sending-method" value="facebook">
                <div class="sending-option-content">
                  <div class="sending-icon">📘</div>
                  <h4>فيسبوك</h4>
                  <p>رسالة على صفحة الفيسبوك<br><strong>Adam Superfoods</strong></p>
                </div>
              </label>
            </div>
          </div>
        </div>
        
        <div class="form-nav">
          <button type="button" class="btn prev-step">السابق</button>
          <button type="button" class="btn gold" id="final-submit">إرسال الآن</button>
        </div>
      </div>
    </form>
    
    <div class="form-success" style="display:none;">
      <div class="success-icon">✓</div>
      <h3>تم تسجيل طلبك بنجاح!</h3>
      <p id="success-message">سيتم توجيهك الآن لإرسال الطلب عبر الطريقة المختارة.</p>
      <div class="cta-row">
        <button id="send-now" class="btn gold pulse-effect">إرسال الآن</button>
        <a href="#products" class="btn">استكشاف المنتجات</a>
      </div>
    </div>
    <style>
      .sending-options {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 15px;
      margin-top: 10px;
    }
    
    .sending-option {
      cursor: pointer;
      margin: 0;
    }
    
    .sending-option input {
      position: absolute;
      opacity: 0;
    }
    
    .sending-option-content {
      border: 1px solid rgba(201,163,90,.2);
      border-radius: var(--radius);
      padding: 20px;
      text-align: center;
      transition: all 0.3s ease;
      height: 100%;
    }
    
    .sending-option input:checked + .sending-option-content {
      border-color: var(--gold);
      background: rgba(201,163,90,.08);
      box-shadow: 0 0 15px rgba(201,163,90,.2);
    }
    
    .sending-icon {
      font-size: 32px;
      margin-bottom: 10px;
    }
    
    .sending-option-content h4 {
      margin: 0 0 10px;
      color: var(--gold);
    }
    
    .sending-option-content p {
      margin: 0;
      font-size: 14px;
      color: var(--text-dim);
    }
    
    @media (max-width: 768px) {
      .sending-options {
        grid-template-columns: 1fr;
      }
    }
    .form-progress {
      display: flex;
      justify-content: space-between;
      margin-bottom: 30px;
      position: relative;
    }
    
    .form-progress::before {
      content: '';
      position: absolute;
      top: 15px;
      left: 0;
      right: 0;
      height: 2px;
      background: rgba(201,163,90,.2);
      z-index: 1;
    }
    
    .progress-step {
      position: relative;
      z-index: 2;
      background: var(--dark);
      padding: 0 15px;
      color: var(--text-dim);
      font-size: 14px;
      font-weight: 600;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    
    .progress-step::before {
      content: attr(data-step);
      width: 30px;
      height: 30px;
      border-radius: 50%;
      background: var(--dark);
      border: 2px solid rgba(201,163,90,.2);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 8px;
      transition: all 0.3s ease;
    }
    
    .progress-step.active {
      color: var(--gold);
    }
    
    .progress-step.active::before {
      background: var(--gold);
      border-color: var(--gold);
      color: var(--dark);
      box-shadow: 0 0 15px rgba(201,163,90,.5);
    }
    
    .progress-step.completed::before {
      content: '✓';
      background: var(--gold);
      border-color: var(--gold);
      color: var(--dark);
    }
    
    .form-step-container {
      display: none;
    }
    
    .form-step-container.active {
      display: block;
      animation: fadeIn 0.5s ease;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    .form-nav {
      display: flex;
      justify-content: space-between;
      margin-top: 20px;
    }
    
    .form-hint {
      font-size: 12px;
      color: var(--text-dim);
      margin-top: 5px;
    }
    
    .partnership-types {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 15px;
      margin-top: 10px;
    }
    
    .partnership-type {
      cursor: pointer;
      margin: 0;
    }
    
    .partnership-type input {
      position: absolute;
      opacity: 0;
    }
    
    .partnership-type-content {
      border: 1px solid rgba(201,163,90,.2);
      border-radius: var(--radius);
      padding: 20px;
      text-align: center;
      transition: all 0.3s ease;
      height: 100%;
    }
    
    .partnership-type input:checked + .partnership-type-content {
      border-color: var(--gold);
      background: rgba(201,163,90,.08);
      box-shadow: 0 0 15px rgba(201,163,90,.2);
    }
    
    .partnership-icon {
      font-size: 32px;
      margin-bottom: 10px;
    }
    
    .partnership-type-content h4 {
      margin: 0 0 10px;
      color: var(--gold);
    }
    
    .partnership-type-content p {
      margin: 0;
      font-size: 14px;
      color: var(--text-dim);
    }
    
    .form-success {
      text-align: center;
      padding: 40px 20px;
      animation: fadeIn 0.5s ease;
    }
    
    .success-icon {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: var(--gold);
      color: var(--dark);
      font-size: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 20px;
      box-shadow: 0 0 20px rgba(201,163,90,.5);
    }
    
    @media (max-width: 768px) {
      .partnership-types {
        grid-template-columns: 1fr;
      }
      
      .progress-step {
        font-size: 12px;
        padding: 0 5px;
      }
    }
    </style>
    </div>
</footer>

<!-- KPIs / Stats -->
<section class="section big-hook" id="stats">
  <div class="container">
    <div class="title">
      <h5>Key Numbers</h5>
      <h2>أرقام تتحدث <span style="color:var(--gold);">عن نتائج حقيقية</span></h2>
      <p class="lead">خبرة تقود الثقة، وثقة تصنع نموًا مستدامًا. <strong>شركاء يختارون البقاء معنا عامًا بعد عام.</strong></p>
    </div>
    <div class="kpi-grid">
      <div class="kpi">
        <div class="num">ثلاثون عاما</div>
        <div class="lbl">من الخبرة والابداع <span style="color:var(--gold);">(ضمان الجودة)</span></div>
      </div>
      <div class="kpi">
        <div class="num">98%</div>
        <div class="lbl">معدل الاحتفاظ بالعملاء <span style="color:var(--gold);">(شراكة مستدامة)</span></div>
      </div>
      <div class="kpi">
        <div class="num">50+</div>
        <div class="lbl">دولة حول العالم <span style="color:var(--gold);">(خبرة عالمية)</span></div>
      </div>
      <div class="kpi">
        <div class="num">100%</div>
        <div class="lbl">منتجات مصرية خالصة <span style="color:var(--gold);">(قصة تسويقية فريدة)</span></div>
      </div>
    </div>
  </div>
</section>

<section class="section fold" id="limited-offer">
  <div class="container">
    <div class="title">
      <h5>عرض محدود</h5>
      <h2>فرصة <span style="color:var(--gold);">حصرية ومحدودة</span> للشركاء الجدد</h2>
      <p class="lead">نستقبل عدد محدود من الشركاء الجدد هذا الربع. احجز مكانك الآن واحصل على مزايا حصرية</p>
    </div>
    
    <div class="countdown-container">
      <div class="countdown-card">
        <div class="countdown-timer">
          <div class="countdown-unit">
            <div class="countdown-value" id="countdown-days">14</div>
            <div class="countdown-label">يوم</div>
          </div>
          <div class="countdown-separator">:</div>
          <div class="countdown-unit">
            <div class="countdown-value" id="countdown-hours">23</div>
            <div class="countdown-label">ساعة</div>
          </div>
          <div class="countdown-separator">:</div>
          <div class="countdown-unit">
            <div class="countdown-value" id="countdown-minutes">59</div>
            <div class="countdown-label">دقيقة</div>
          </div>
          <div class="countdown-separator">:</div>
          <div class="countdown-unit">
            <div class="countdown-value" id="countdown-seconds">59</div>
            <div class="countdown-label">ثانية</div>
          </div>
        </div>
        
        <div class="countdown-offer">
          <div class="offer-badge">عرض خاص</div>
          <h3>احصل على خصم <span>15%</span> على الطلبية الأولى</h3>
          <p>+ باقة عينات مجانية بقيمة 500$ + مواد تسويقية حصرية</p>
          <div class="offer-note">* العرض متاح للشركاء الجدد فقط ولفترة محدودة</div>
        </div>
      </div>
      
      <div class="cta-row">
        <a href="#contact" class="btn gold pulse-gold">احجز شراكتك الآن</a>
      </div>
    </div>
    
    <style>
      .countdown-container {
        margin: 40px 0;
      }
      
      .countdown-card {
        background: linear-gradient(135deg, rgba(20,20,20,.9) 0%, rgba(30,30,30,.9) 100%);
        border: 1px solid rgba(201,163,90,.3);
        border-radius: var(--radius);
        padding: 30px;
        box-shadow: var(--shadow-soft);
        position: relative;
        overflow: hidden;
      }
      
      .countdown-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('https://uc948872988958016b0785821a04.previews.dropboxusercontent.com/p/thumb/ACs6Ls4CimZvv0DUls_iSoIfCL9uOomRHbITiActuw-JD-9RMeBpuZCuA8EUO1qchYA-sJG91zRUyIZCQnz5Lt0dtDyjlYdmzspu7ea7qkLDastLzcrCPIsBL-2KlYt9Hyx7op83ovZhRhYK3On6Sqk-B-akrU0CG38wHGasTaT6qTKR6-gu50s95vcS-v8qp7q8r2Jj5MfN2eLB960NFcajxkSAUzmJLaBWm5oxo3lQgwGN3K64Lej5nfBH4uxubUm0GGhLHBQcatxHRpsPmMc-ZLzfeVHptBDcolj23I9lVfxEPWfNsSzkLh8s2D3lNbuG-0yU5M7O51yrv_fq_m9VPM8x1MV1oQkeXF2d7giLlS9u3bEfDmzHcKYz4qWtDrX3tYmOpBazlHcT4ulW_YNN/p.jpeg?is_prewarmed=true') center/cover no-repeat;
        opacity: 0.1;
        z-index: -1;
      }
      
      .countdown-timer {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 30px;
      }
      
      .countdown-unit {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin: 0 5px;
      }
      
      .countdown-value {
        font-size: 48px;
        font-weight: 700;
        color: var(--gold);
        text-shadow: 0 0 10px rgba(201,163,90,.5);
        background: rgba(201,163,90,.1);
        border-radius: 8px;
        padding: 10px 15px;
        min-width: 80px;
        text-align: center;
        box-shadow: inset 0 0 15px rgba(0,0,0,.2), 0 0 5px rgba(201,163,90,.3);
        border: 1px solid rgba(201,163,90,.2);
      }
      
      .countdown-label {
        font-size: 14px;
        color: var(--text-dim);
        margin-top: 5px;
      }
      
      .countdown-separator {
        font-size: 36px;
        font-weight: 700;
        color: var(--gold);
        margin-top: -20px;
      }
      
      .countdown-offer {
        text-align: center;
        padding: 20px;
        background: rgba(201,163,90,.08);
        border-radius: var(--radius);
        border: 1px dashed rgba(201,163,90,.3);
        position: relative;
      }
      
      .offer-badge {
        position: absolute;
        top: -15px;
        right: 20px;
        background: var(--gold);
        color: var(--dark);
        font-weight: 700;
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 14px;
        box-shadow: 0 3px 10px rgba(0,0,0,.2);
      }
      
      .countdown-offer h3 {
        margin: 10px 0;
        font-size: 24px;
      }
      
      .countdown-offer h3 span {
        color: var(--gold);
        font-size: 32px;
      }
      
      .countdown-offer p {
        margin: 10px 0;
        color: var(--text);
      }
      
      .offer-note {
        font-size: 12px;
        color: var(--text-dim);
        margin-top: 15px;
      }
      
      .pulse-gold {
        animation: pulse-gold 2s infinite;
      }
      
      @keyframes pulse-gold {
        0% {
          box-shadow: 0 0 0 0 rgba(201,163,90,.7);
        }
        70% {
          box-shadow: 0 0 0 15px rgba(201,163,90,0);
        }
        100% {
          box-shadow: 0 0 0 0 rgba(201,163,90,0);
        }
      }
      
      @media (max-width: 768px) {
        .countdown-timer {
          flex-wrap: wrap;
        }
        
        .countdown-value {
          font-size: 32px;
          min-width: 60px;
        }
        
        .countdown-separator {
          font-size: 24px;
          margin-top: -10px;
        }
      }
    </style>
  </div>
</section>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // تفعيل جدول المقارنة التفاعلي
    const comparisonRows = document.querySelectorAll('.comparison-row');
    comparisonRows.forEach(row => {
      row.addEventListener('click', function() {
        this.classList.toggle('active');
      });
    });

    // تفعيل العداد التنازلي
    function updateCountdown() {
      // تعيين تاريخ انتهاء العرض (بعد 15 يوم من الآن)
      const now = new Date();
      const endDate = new Date();
      endDate.setDate(now.getDate() + 15);
      endDate.setHours(23, 59, 59, 0);
      
      const timeLeft = endDate - now;
      
      if (timeLeft <= 0) {
        // إعادة تعيين العداد إذا انتهى الوقت
        endDate.setDate(now.getDate() + 15);
      }
      
      const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
      const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
      
      document.getElementById('countdown-days').textContent = days.toString().padStart(2, '0');
      document.getElementById('countdown-hours').textContent = hours.toString().padStart(2, '0');
      document.getElementById('countdown-minutes').textContent = minutes.toString().padStart(2, '0');
      document.getElementById('countdown-seconds').textContent = seconds.toString().padStart(2, '0');
    }
    
    // تحديث العداد كل ثانية
    updateCountdown();
    setInterval(updateCountdown, 1000);
    
    // تفعيل نموذج الشراكة متعدد الخطوات
    // ... existing code ...
    // تفعيل نموذج الشراكة متعدد الخطوات
    const formSteps = document.querySelectorAll('.form-step-container');
    const progressSteps = document.querySelectorAll('.progress-step');
    const nextButtons = document.querySelectorAll('.next-step');
    const prevButtons = document.querySelectorAll('.prev-step');
    const partnerForm = document.getElementById('partner-form');
    const formSuccess = document.querySelector('.form-success');
    const finalSubmitBtn = document.getElementById('final-submit');
    const sendNowBtn = document.getElementById('send-now');
    
    let currentStep = 0;
    let formData = {};
    
    function showStep(stepIndex) {
      formSteps.forEach((step, index) => {
        step.classList.toggle('active', index === stepIndex);
      });
      
      progressSteps.forEach((step, index) => {
        step.classList.toggle('active', index === stepIndex);
        step.classList.toggle('completed', index < stepIndex);
      });
      
      currentStep = stepIndex;
    }
    
    function collectFormData() {
      const form = document.getElementById('partner-form');
      const formDataObj = new FormData(form);
      
      formData = {
        companyName: document.getElementById('company-name').value,
        companyType: document.getElementById('company-type').value,
        location: document.getElementById('location').value,
        experience: document.getElementById('experience').value,
        partnershipType: formDataObj.get('partnership-type'),
        investmentRange: document.getElementById('investment-range').value,
        timeline: document.getElementById('timeline').value,
        contactName: document.getElementById('contact-name').value,
        contactPosition: document.getElementById('contact-position').value,
        contactEmail: document.getElementById('contact-email').value,
        contactPhone: document.getElementById('contact-phone').value,
        message: document.getElementById('message').value,
        sendingMethod: formDataObj.get('sending-method')
      };
    }
    
    function generateMessage() {
      return `🤝 طلب شراكة جديد - آدم سوبرفودز\n\n` +
             `📋 بيانات الشركة:\n` +
             `• اسم الشركة: ${formData.companyName}\n` +
             `• نوع النشاط: ${formData.companyType}\n` +
             `• الموقع: ${formData.location}\n` +
             `• سنوات الخبرة: ${formData.experience}\n\n` +
             `🎯 تفاصيل الشراكة:\n` +
             `• نوع الشراكة: ${getPartnershipTypeText(formData.partnershipType)}\n` +
             `• حجم الاستثمار: ${getInvestmentRangeText(formData.investmentRange)}\n` +
             `• الإطار الزمني: ${getTimelineText(formData.timeline)}\n\n` +
             `👤 بيانات المسؤول:\n` +
             `• الاسم: ${formData.contactName}\n` +
             `• المنصب: ${formData.contactPosition}\n` +
             `• الإيميل: ${formData.contactEmail}\n` +
             `• الهاتف: ${formData.contactPhone}\n\n` +
             `💬 الرسالة:\n${formData.message || 'لا توجد رسالة إضافية'}\n\n` +
             `⏰ تاريخ الطلب: ${new Date().toLocaleDateString('ar-EG')}`;
    }
    
    function getPartnershipTypeText(type) {
      const types = {
        'distribution': 'توزيع حصري',
        'retail': 'بيع بالتجزئة',
        'white-label': 'علامة خاصة',
        'franchise': 'امتياز تجاري'
      };
      return types[type] || type;
    }
    
    function getInvestmentRangeText(range) {
      const ranges = {
        'small': 'أقل من 50,000 جنيه',
        'medium': '50,000 - 200,000 جنيه',
        'large': 'أكثر من 200,000 جنيه'
      };
      return ranges[range] || range;
    }
    
    function getTimelineText(timeline) {
      const timelines = {
        'immediate': 'فوري (خلال شهر)',
        'short': 'قريب (1-3 أشهر)',
        'medium': 'متوسط (3-6 أشهر)',
        'long': 'بعيد (أكثر من 6 أشهر)'
      };
      return timelines[timeline] || timeline;
    }
    
    function sendMessage() {
      const message = generateMessage();
      const encodedMessage = encodeURIComponent(message);
      
      switch(formData.sendingMethod) {
        case 'whatsapp':
          window.open(`https://wa.me/201094443768?text=${encodedMessage}`, '_blank');
          break;
        case 'telegram':
          window.open(`https://t.me/+201094443768?text=${encodedMessage}`, '_blank');
          break;
        case 'email':
          window.open(`mailto:<EMAIL>?subject=طلب شراكة - ${formData.companyName}&body=${encodedMessage}`, '_blank');
          break;
        case 'facebook':
          window.open('https://www.facebook.com/adamsuperfoods', '_blank');
          alert('سيتم فتح صفحة الفيسبوك. يرجى إرسال رسالة تحتوي على بيانات طلب الشراكة.');
          break;
      }
    }
    
    nextButtons.forEach(button => {
      button.addEventListener('click', function() {
        const currentFormStep = formSteps[currentStep];
        const requiredFields = currentFormStep.querySelectorAll('[required]');
        let isValid = true;
        
        requiredFields.forEach(field => {
          if (!field.value && field.type !== 'radio') {
            isValid = false;
            field.classList.add('invalid');
          } else if (field.type === 'radio') {
            const radioGroup = currentFormStep.querySelectorAll(`[name="${field.name}"]`);
            const isRadioChecked = Array.from(radioGroup).some(radio => radio.checked);
            if (!isRadioChecked) {
              isValid = false;
              radioGroup.forEach(radio => radio.closest('.partnership-type, .sending-option').classList.add('invalid'));
            } else {
              radioGroup.forEach(radio => radio.closest('.partnership-type, .sending-option').classList.remove('invalid'));
            }
          } else {
            field.classList.remove('invalid');
          }
        });
        
        if (isValid && currentStep < formSteps.length - 1) {
          showStep(currentStep + 1);
        }
      });
    });
    
    prevButtons.forEach(button => {
      button.addEventListener('click', function() {
        if (currentStep > 0) {
          showStep(currentStep - 1);
        }
      });
    });
    
    partnerForm.addEventListener('submit', function(e) {
      e.preventDefault();
      
      const currentFormStep = formSteps[currentStep];
      const requiredFields = currentFormStep.querySelectorAll('[required]');
      let isValid = true;
      
      requiredFields.forEach(field => {
        if (!field.value) {
          isValid = false;
          field.classList.add('invalid');
        } else {
          field.classList.remove('invalid');
        }
      });
      
      if (isValid && currentStep < formSteps.length - 1) {
        showStep(currentStep + 1);
      }
    });
    
    finalSubmitBtn.addEventListener('click', function() {
      const sendingMethodSelected = document.querySelector('[name="sending-method"]:checked');
      
      if (!sendingMethodSelected) {
        alert('يرجى اختيار طريقة الإرسال المفضلة');
        return;
      }
      
      collectFormData();
      
      partnerForm.style.display = 'none';
      document.querySelector('.form-progress').style.display = 'none';
      formSuccess.style.display = 'block';
      
      const successMessage = document.getElementById('success-message');
      const methodText = {
        'whatsapp': 'واتساب',
        'telegram': 'تليجرام', 
        'email': 'البريد الإلكتروني',
        'facebook': 'فيسبوك'
      };
      
      successMessage.textContent = `تم تسجيل طلبك بنجاح! اضغط على "إرسال الآن" لإرسال الطلب عبر ${methodText[formData.sendingMethod]}.`;
    });
    
    sendNowBtn.addEventListener('click', sendMessage);
    
    // تفعيل حاسبة العائد على الاستثمار المحدثة
    const customerTypeSelect = document.getElementById('customer-type');
    const salesVolumeInput = document.getElementById('sales-volume');
    const profitMarginInput = document.getElementById('profit-margin');
    
    const salesVolumeValue = document.getElementById('sales-volume-value');
    const profitMarginValue = document.getElementById('profit-margin-value');
    
    const monthlyRevenueOutput = document.getElementById('monthly-revenue');
    const monthlyProfitOutput = document.getElementById('monthly-profit');
    const annualRoiOutput = document.getElementById('annual-roi');
    const consumerUnitsOutput = document.getElementById('consumer-units');
    
    function updateROICalculator() {
      const customerType = customerTypeSelect.value;
      const salesVolume = parseInt(salesVolumeInput.value);
      const profitMargin = parseInt(profitMarginInput.value) / 100;
      
      // أسعار البيع حسب نوع العميل
      const prices = {
        'agent': 89,
        'wholesaler': 99,
        'retail': 110
      };
      
      const unitPrice = prices[customerType];
      
      salesVolumeValue.textContent = salesVolume.toLocaleString();
      profitMarginValue.textContent = `${profitMarginInput.value}%`;
      
      const monthlyRevenue = salesVolume * unitPrice * 12;
      const monthlyProfit = monthlyRevenue * profitMargin;
      const annualProfit = monthlyProfit * 12;
      
      // حساب الاستثمار الأولي (تقدير بناءً على حجم المبيعات)
      const initialInvestment = salesVolume * unitPrice * 1.5; // تقدير للمخزون الأولي
      const annualROI = (annualProfit / initialInvestment) * 100;
      
      // حساب عدد العبوات للمستهلك النهائي (كل كرتونة = 12 عبوة)
      const consumerUnits = salesVolume * 12;
      
      monthlyRevenueOutput.textContent = `${monthlyRevenue.toLocaleString()} جنيه`;
      monthlyProfitOutput.textContent = `${monthlyProfit.toLocaleString()} جنيه`;
      annualRoiOutput.textContent = `${Math.round(annualROI)}%`;
      consumerUnitsOutput.textContent = `${consumerUnits.toLocaleString()} عبوة`;
      
      // تحديث النصائح حسب نوع العميل
      updateProfitTips(customerType, salesVolume);
    }
    
    function updateProfitTips(customerType, salesVolume) {
      const tipsElement = document.getElementById('profit-tips');
      let tips = [];
      
      if (customerType === 'agent') {
        tips = [
          `كوكيل، يمكنك البيع لتجار الجملة والتجزئة بأسعار تنافسية`,
          `حجم المبيعات الحالي: ${salesVolume} كرتونة شهرياً`,
          `إمكانية الوصول لحجم 2000 كرتونة شهرياً مع التوسع`,
          `التركيز على بناء شبكة موزعين قوية يضمن نمو مستدام`
        ];
      } else if (customerType === 'wholesaler') {
        tips = [
          `كتاجر جملة، ركز على تجار التجزئة والمتاجر الصغيرة`,
          `هامش ربح جيد مع سعر البيع 99 جنيه للكرتونة`,
          `بناء علاقات طويلة المدى مع العملاء يحقق استقرار في المبيعات`,
          `التوسع في المناطق الجديدة يزيد من حجم المبيعات`
        ];
      } else {
        tips = [
          `السلاسل التجارية والأنشطة المتخصصة تحقق مبيعات مستقرة`,
          `العملاء المهتمون بالجودة يشترون بانتظام (عبوة كل 10-14 يوم)`,
          `التسويق للقيمة الصحية يجذب عملاء أكثر التزاماً`,
          `الشراكة مع أطباء التغذية تفتح أسواق جديدة`
        ];
      }
      
      tipsElement.innerHTML = tips.map(tip => `<li>${tip}</li>`).join('');
    }
    
    customerTypeSelect.addEventListener('change', updateROICalculator);
    salesVolumeInput.addEventListener('input', updateROICalculator);
    profitMarginInput.addEventListener('input', updateROICalculator);
    
    // تحديث القيم الأولية
    updateROICalculator();
// ... existing code ...
  });
</script>
</body>
</html>


  