/**
 * ملف تجاوب سطح المكتب
 *
 * هذا الملف يحتوي على أنماط تجاوب سطح المكتب (أكبر من 992px)
 *
 * الأنماط المعرفة في هذا الملف:
 * - استجابة للشاشات الكبيرة جدًا (أكبر من 1200px)
 * - استجابة للشاشات الكبيرة (992px - 1199px)
 * - تنسيقات الصور في صفحة About
 * - تنسيقات الفقرات للشاشات الكبيرة
 * - تنسيقات صفحة Molasses للشاشات الكبيرة
 * - تنسيقات الصور كاملة العرض للشاشات الكبيرة
 *
 * المتغيرات والدوال المستوردة من ملفات أخرى:
 * - من ملف abstracts/_variables.scss: $breakpoint-xl, $paragraphImgWidth, $borderRadius
 * - من ملف abstracts/_mixins.scss: respond-to()
 * - من ملف abstracts/_breakpoints.scss: $breakpoints
 */

// =============================
// تم تبسيط الاستدعاءات: الآن نعتمد فقط على ملف أدوات الملاءمة الموحد
@use './_responsive-helpers.scss' as *;
@use "sass:map";

// الشاشات الكبيرة (أكبر من 1200px)
@media screen and (min-width: $breakpoint-xl) {
  .container {
    max-width: 85vw;
  }
}

// الشاشات المتوسطة الكبيرة (992px - 1199px)
@include media('xl') {
  .container {
    max-width: 90vw;
  }

  // تعديلات للعناوين
  h1 {
    font-size: 2.5rem;
  }

  h2 {
    font-size: 2rem;
  }
}


@include media('md', 'landscape') {
  // تنسيقات الصور في صفحة Tahini
  .about-page .paragraphs-wraper .paragraph .paragraph-img img.T1 {
    border-radius: 50%;
    display: block;
    height: 200px;
    object-fit: cover;
    object-position: center;
    position: relative;
    width: 200px;
    z-index: 1;
  }
}


/*
  =============================
  أنماط التجاوب لسطح المكتب (ديسكتوب)
  منقولة من الهيكل الاحترافي public/styles/responsive/_desktop.scss
  - تشمل: تصحيح العناوين، الصور، الفقرات، صفحات molasses/tahini، الصور الدائرية، الصور الكاملة
  =============================
*/
