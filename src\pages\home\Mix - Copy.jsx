// SuperFoodsUniverse.jsx
import React, { useState } from "react";
import styles from "./Mix.module.scss";

// Images (replace paths with your actual imports)
import HeroImg from "@images/molasses/1.webp";
import MolassesImg from "@images/molasses/6.webp";
import TahiniImg from "@/images/tahini/3.webp";
import ComboImg from "molasses/9.webp";
import Salad from "@/assets/salad.jpg";
import Waffles from "@/images/imgs/waffles.jpg";
import Smoothies from "@/images/imgs/smoothies.jpg";

function NutritionTable({ data }) {
  const [open, setOpen] = useState(false);

  return (
    <div className={styles.nutritionWrap}>
      <button onClick={() => setOpen(!open)} className={styles.toggleBtn}>
        {open ? "Hide Nutrition Facts" : "View Nutrition Facts"}
      </button>

      {open && (
        <div className={styles.tableContainer}>
          <table className={styles.table}>
            <thead>
              <tr>
                <th>Nutrient</th>
                <th>per 100g</th>
              </tr>
            </thead>
            <tbody>
              {Object.entries(data).map(([nutrient, value]) => (
                <tr key={nutrient}>
                  <td>{nutrient}</td>
                  <td>{value}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}

export default function Mix() {
  const molassesNutrition = {
    Energy: "210.9 Kcal",
    Protein: "1.1 g",
    Carbohydrates: "54.5 g",
    "Total Sugars": "50 g",
    Iron: "10.7 mg",
    Calcium: "60–205 mg",
    Magnesium: "242 mg",
    Potassium: "1460 mg",
    "Vitamin B6": "0.67 mg",
    Polyphenols: "497 mg GAE",
  };

  const tahiniNutrition = {
    Energy: "594 Kcal",
    Protein: "17.4 g",
    Fat: "53.7 g",
    Carbohydrates: "21.2 g",
    Fiber: "9.3 g",
    Calcium: "430 mg",
    Magnesium: "95 mg",
    Phosphorus: "740 mg",
    Iron: "9 mg",
    Zinc: "5 mg",
    "Vitamin B1": "1.22 mg",
    Folate: "98 µg",
    Arginine: "2520 mg",
  };

  return (
    <div className={styles.page}>
      {/* NAVBAR */}
      <header className={styles.header}>
        <nav className={styles.navInner}>
          <div className={styles.navLeft}>
            <a href="/adam/molasses" className={styles.navLink}>Adam Molasses</a>
            <a href="/mentor/superfoods" className={styles.navActive}>Mentor Superfoods</a>
          </div>
          <a href="/shop" className={styles.shopBtn}>Shop Now</a>
        </nav>
      </header>

      {/* HERO */}
      <section className={styles.hero}>
        <div className={styles.heroGrid}>
          <div className={styles.heroText}>
            <h1 className={styles.h1}>SuperFoods World</h1>
            <p className={styles.lead}>
              Discover the luxury of natural nutrition. From{" "}
              <span className={styles.highlight}>Adam Molasses</span> to{" "}
              <span className={styles.highlight}>Mentor Tahini</span>, each crafted to
              bring authentic taste and valuable nutrients to your lifestyle — whether you’re an athlete, a parent, or a gourmet enthusiast.
            </p>
            <a href="#catalog" className={styles.cta}>Explore Catalog</a>
          </div>

          <div className={styles.heroImage}>
            <img src={HeroImg} alt="Superfoods hero" />
          </div>
        </div>
      </section>

      {/* CATALOG */}
      <section id="catalog" className={styles.catalog}>
        <div className={styles.grid}>
          {/* Molasses */}
          <article className={styles.productCard}>
            <div className={styles.cardHeader}>
              <div>
                <h3>Adam Molasses</h3>
                {/* changed wording to neutral, non-therapeutic */}
                <p className={styles.sub}>Premium natural energy ingredient</p>
              </div>
              <img src={MolassesImg} alt="Adam Molasses" className={styles.thumb}/>
            </div>

            <div className={styles.cardBody}>
              <h4>Nutrition Snapshot</h4>
              <ul>
                {/* made claims factual and supportable */}
                <li>Contains iron and magnesium</li>
                <li>Provides potassium as part of its mineral profile</li>
                <li>No artificial additives at source</li>
              </ul>

              <h5>Usage Ideas</h5>
              <p>Mix into smoothies, drizzle over pancakes, or combine with tahini for a flavorful spread.</p>

              <NutritionTable data={molassesNutrition} />
            </div>

            <a href="/adam/molasses" className={styles.btnPrimary}>View Product</a>
          </article>

          {/* Tahini */}
          <article className={styles.productCard}>
            <div className={styles.cardHeader}>
              <div>
                <h3>Mentor Tahini</h3>
                <p className={styles.sub}>Luxury plant-based ingredient</p>
              </div>
              <img src={TahiniImg} alt="Mentor Tahini" className={styles.thumb}/>
            </div>

            <div className={styles.cardBody}>
              <h4>Nutrition Snapshot</h4>
              <ul>
                <li>Good source of plant protein</li>
                <li>Provides calcium and phosphorus</li>
                <li>Cold-pressed sesame — no oil substitutes</li>
              </ul>

              <h5>Usage Ideas</h5>
              <p>Add to smoothies, use as a spread, or stir into dressings for a creamy texture.</p>

              <NutritionTable data={tahiniNutrition} />
            </div>

            <a href="/mentor/tahini" className={styles.btnAccent}>View Product</a>
          </article>

          {/* Combo */}
          <article className={styles.comboCard}>
            <div className={styles.cardHeader}>
              <div>
                <h3>Molasses + Tahini Combo</h3>
                <p className={styles.sub}>Balance of taste & nutrients</p>
              </div>
              <img src={ComboImg} alt="Combo" className={styles.thumb}/>
            </div>

            <div className={styles.cardBody}>
              <p>
                Together, they form a nutrient-dense combination: molasses contributes natural sugars and minerals, while tahini adds plant protein and healthy fats — a traditional pairing adapted for contemporary kitchens.
              </p>
              <p className={styles.marginTop}>
                Suggested Serving: 2 tbsp molasses + 1 tbsp tahini, stirred into breakfast bowls or spread over toast.
              </p>
            </div>

            <div className={styles.comboActions}>
              <a href="/adam/molasses" className={styles.btnPrimary}>Adam Molasses</a>
              <a href="/mentor/tahini" className={styles.btnOutline}>Mentor Tahini</a>
            </div>
          </article>
        </div>
      </section>

      {/* USAGE IDEAS */}
      <section className={styles.usage}>
        <div className={styles.usageInner}>
          <h3>Smart Usage Examples</h3>
          <p>Quick, creative ways to integrate our superfoods into everyday life.</p>

          <div className={styles.usageGrid}>
            <div className={styles.usageCard}>
              <img src={Salad} alt="Salad" />
              <h4>Salad Dressings</h4>
              <p>Whisk tahini & molasses with lemon for a rich dressing.</p>
            </div>

            <div className={styles.usageCard}>
              <img src={Waffles} alt="Waffles" />
              <h4>Healthy Treats</h4>
              <p>Replace syrups with molasses for a natural topping with deeper flavor.</p>
            </div>

            <div className={styles.usageCard}>
              <img src={Smoothies} alt="Smoothies" />
              <h4>Smoothie Boost</h4>
              <p>Blend tahini & molasses into your morning shake for extra texture and taste.</p>
            </div>
          </div>
        </div>
      </section>

      {/* FOOTER */}
      <footer className={styles.footer}>
        <div>© {new Date().getFullYear()} Adam for foodstuffs. All rights reserved.</div>
        {/* legal disclaimer added */}
        <div className={styles.disclaimer}>
          Nutrition information is based on product analysis. This product is not intended to diagnose, treat, cure, or prevent any disease. For medical advice, consult a qualified professional.
        </div>
      </footer>
    </div>
  );
}
