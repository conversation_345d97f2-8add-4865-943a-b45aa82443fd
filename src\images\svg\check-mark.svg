var _path, _path2, _path3, _path4, _defs;
function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
import * as React from "react";
function SvgCheckMark(_ref, svgRef) {
  let {
    title,
    titleId,
    ...props
  } = _ref;
  return /*#__PURE__*/React.createElement("svg", _extends({
    width: "800px",
    height: "800px",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    d: "M4.91988 12.257C4.2856 12.257 3.65131 12.5199 3.19988 13.0342C2.79417 13.4913 2.59417 14.0799 2.63417 14.6913C2.67417 15.3027 2.94846 15.857 3.4056 16.2627L7.51417 19.8684C7.93131 20.2342 8.46846 20.4399 9.02274 20.4399C9.0856 20.4399 9.14846 20.4399 9.21131 20.4342C9.82846 20.3827 10.4056 20.0799 10.7942 19.5999L20.857 7.27986C21.657 6.30272 21.5085 4.85701 20.5313 4.05701C20.057 3.67415 19.4627 3.49129 18.857 3.55415C18.2513 3.61701 17.7027 3.90844 17.3142 4.38272L8.74846 14.8627L6.42274 12.8227C5.99417 12.4456 5.45131 12.257 4.91988 12.257Z",
    fill: "url(#paint0_linear)"
  })), _path2 || (_path2 = /*#__PURE__*/React.createElement("path", {
    d: "M9.02279 20.0284C8.56565 20.0284 8.12565 19.8627 7.78279 19.5598L3.67422 15.9541C2.89708 15.2684 2.81708 14.0798 3.50279 13.3027C4.18851 12.5255 5.37708 12.4455 6.15422 13.1313L8.79994 15.4513L17.6285 4.63983C18.2856 3.83412 19.4685 3.71983 20.2742 4.37126C21.0799 5.0284 21.1942 6.21126 20.5428 7.01697L10.4742 19.337C10.1542 19.7313 9.67993 19.977 9.17708 20.0227C9.12565 20.0227 9.07422 20.0284 9.02279 20.0284Z",
    fill: "url(#paint1_linear)"
  })), _path3 || (_path3 = /*#__PURE__*/React.createElement("path", {
    opacity: 0.75,
    d: "M9.02279 20.0284C8.56565 20.0284 8.12565 19.8627 7.78279 19.5598L3.67422 15.9541C2.89708 15.2684 2.81708 14.0798 3.50279 13.3027C4.18851 12.5255 5.37708 12.4455 6.15422 13.1313L8.79994 15.4513L17.6285 4.63983C18.2856 3.83412 19.4685 3.71983 20.2742 4.37126C21.0799 5.0284 21.1942 6.21126 20.5428 7.01697L10.4742 19.337C10.1542 19.7313 9.67993 19.977 9.17708 20.0227C9.12565 20.0227 9.07422 20.0284 9.02279 20.0284Z",
    fill: "url(#paint2_radial)"
  })), _path4 || (_path4 = /*#__PURE__*/React.createElement("path", {
    opacity: 0.5,
    d: "M9.02279 20.0284C8.56565 20.0284 8.12565 19.8627 7.78279 19.5598L3.67422 15.9541C2.89708 15.2684 2.81708 14.0798 3.50279 13.3027C4.18851 12.5255 5.37708 12.4455 6.15422 13.1313L8.79994 15.4513L17.6285 4.63983C18.2856 3.83412 19.4685 3.71983 20.2742 4.37126C21.0799 5.0284 21.1942 6.21126 20.5428 7.01697L10.4742 19.337C10.1542 19.7313 9.67993 19.977 9.17708 20.0227C9.12565 20.0227 9.07422 20.0284 9.02279 20.0284Z",
    fill: "url(#paint3_radial)"
  })), _defs || (_defs = /*#__PURE__*/React.createElement("defs", null, /*#__PURE__*/React.createElement("linearGradient", {
    id: "paint0_linear",
    x1: 15.825,
    y1: -13.9667,
    x2: 9.82533,
    y2: 23.9171,
    gradientUnits: "userSpaceOnUse"
  }, /*#__PURE__*/React.createElement("stop", {
    stopColor: "#00CC00"
  }), /*#__PURE__*/React.createElement("stop", {
    offset: 0.1878,
    stopColor: "#06C102"
  }), /*#__PURE__*/React.createElement("stop", {
    offset: 0.5185,
    stopColor: "#17A306"
  }), /*#__PURE__*/React.createElement("stop", {
    offset: 0.9507,
    stopColor: "#33740C"
  }), /*#__PURE__*/React.createElement("stop", {
    offset: 1,
    stopColor: "#366E0D"
  })), /*#__PURE__*/React.createElement("linearGradient", {
    id: "paint1_linear",
    x1: 15.2501,
    y1: 0.625426,
    x2: 7.43443,
    y2: 23.6215,
    gradientUnits: "userSpaceOnUse"
  }, /*#__PURE__*/React.createElement("stop", {
    offset: 0.2544,
    stopColor: "#90D856"
  }), /*#__PURE__*/React.createElement("stop", {
    offset: 0.736,
    stopColor: "#00CC00"
  }), /*#__PURE__*/React.createElement("stop", {
    offset: 0.7716,
    stopColor: "#0BCD07"
  }), /*#__PURE__*/React.createElement("stop", {
    offset: 0.8342,
    stopColor: "#29CF18"
  }), /*#__PURE__*/React.createElement("stop", {
    offset: 0.9166,
    stopColor: "#59D335"
  }), /*#__PURE__*/React.createElement("stop", {
    offset: 1,
    stopColor: "#90D856"
  })), /*#__PURE__*/React.createElement("radialGradient", {
    id: "paint2_radial",
    cx: 0,
    cy: 0,
    r: 1,
    gradientUnits: "userSpaceOnUse",
    gradientTransform: "translate(15.452 8.95803) rotate(116.129) scale(8.35776 4.28316)"
  }, /*#__PURE__*/React.createElement("stop", {
    stopColor: "#FBE07A",
    stopOpacity: 0.75
  }), /*#__PURE__*/React.createElement("stop", {
    offset: 0.0803394,
    stopColor: "#FBE387",
    stopOpacity: 0.6897
  }), /*#__PURE__*/React.createElement("stop", {
    offset: 0.5173,
    stopColor: "#FDF2C7",
    stopOpacity: 0.362
  }), /*#__PURE__*/React.createElement("stop", {
    offset: 0.8357,
    stopColor: "#FFFBF0",
    stopOpacity: 0.1233
  }), /*#__PURE__*/React.createElement("stop", {
    offset: 1,
    stopColor: "white",
    stopOpacity: 0
  })), /*#__PURE__*/React.createElement("radialGradient", {
    id: "paint3_radial",
    cx: 0,
    cy: 0,
    r: 1,
    gradientUnits: "userSpaceOnUse",
    gradientTransform: "translate(11.6442 17.0245) rotate(155.316) scale(9.80163 4.14906)"
  }, /*#__PURE__*/React.createElement("stop", {
    stopColor: "#440063",
    stopOpacity: 0.25
  }), /*#__PURE__*/React.createElement("stop", {
    offset: 1,
    stopColor: "#420061",
    stopOpacity: 0
  })))));
}
const ForwardRef = /*#__PURE__*/React.forwardRef(SvgCheckMark);
export default __webpack_public_path__ + "static/media/check-mark.8a6b1ebfe64ffebe412b528e5dfe7f58.svg";
export { ForwardRef as ReactComponent };