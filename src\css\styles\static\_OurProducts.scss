@use './Variables' as *;


.our-products {
  background-image: url('@images/imgs/bg-body4.jpg');
  border-bottom: 1px solid #444;
  padding: 80px 0 40px;
  width: 100%;
  .container {
    .our-products-wraper {
      align-items: center;
      display: flex;
      flex-direction: column;
      width: 100%;
      h5 {
        color: $mainColor;
        font-size: 14px;
        font-weight: 600;
        letter-spacing: 1px;
        margin-bottom: 14px;
      }
      .title {
        color: #fbfbef;
        font-size: 36px;
        font-weight: 600;
      }
      .products-container {
        height: 435px;
        margin-top: 50px;
        width: 100%;
        .swiper {
          height: 100%;
          width: 100%;
          .swiper-button-next {
            right: 180px;
            top: 20px;
            width: 40px;
            &::after {
              border: 2px solid $mainColor;
              border-left-color: transparent;
              border-top-color: transparent;
              content: "";
              height: 5px;
              right: 0;
              transform: rotate(-45deg);
              width: 8px;
            }
            &::before {
              background-color: $mainColor;
              content: "";
              height: 2px;
              right: -5px;
              width: 40px;
            }
          }
          .swiper-button-prev {
            left: unset;
            right: 240px;
            top: 20px;
            width: 40px;
            &::before {
              border: 2px solid $mainColor;
              border-bottom-color: transparent;
              border-right-color: transparent;
              content: "";
              height: 5px;
              transform: rotate(-45deg);
              width: 8px;
            }
            &::after {
              background-color: $mainColor;
              content: "";
              height: 2px;
              left: -5px;
              width: 50px;
            }
          }
          .swiper-slide {
            display: flex;
            height: 100%;
            width: 100%;
            .slide-number {
              display: flex;
              flex-direction: column;
              height: 100%;
              justify-content: center;
              padding: 0 30px;
              width: 20%;
              p {
                color: #fbfbef;
                font-size: 60px;
                font-weight: 100;
                position: relative;
                text-align: left;
                width: 100%;
                &:first-of-type {
                  &::after {
                    background-color: $mainColor;
                    bottom: 0;
                    content: "";
                    height: 1px;
                    position: absolute;
                    right: 50%;
                    transform: rotate(-20deg);
                    width: 25px;
                  }
                }
                &:last-of-type {
                  opacity: .2;
                  text-align: right;
                }
              }
            }
            .product-img {
              background: linear-gradient(to bottom, #111 0%, #333 100%);
              border: 1px solid #444;
              height: 100%;
              overflow: hidden;
              width: 50%;
              img {
                height: 100%;
                object-fit: cover;
                width: 100%;
              }
            }
            .product-info {
              height: 100%;
              margin-left: -40px;
              margin-top: 70px;
              width: 30%;
              h3 {
                color: #fbfbef;
                font-size: 36px;
                font-weight: 600;
                line-height: 1;
              }
              small {
                color: $mainColor;
                display: block;
                font-size: 12px;
                font-weight: 600;
                margin-top: 30px;
                text-align: center;
                width: 100%;
              }
            }
          }
          &.ar {
            font-family: 'Noto Kufi Arabic', Arial, sans-serif !important;
            .product-info {
              h3 {
                font-size: 30px !important;
                line-height: 1.3 !important;
              }
            }
          }
        }
      }
    }
  }
}
// استجابة للشاشات الصغيرة
@media screen and (width <= 480px) {
  .our-products {
    padding: 80px 0;
    .container {
      .our-products-wraper {
        .products-container {
          height: fit-content;
          .swiper {
            width: 95%;
            .swiper-button-next, .swiper-button-prev {
              display: none;
            }
            .swiper-slide {
              flex-direction: column;
              .slide-number {
                display: none;
              }
              .product-img {
                width: 100%;
              }
              .product-info {
                width: 100%;
                margin: 0;
                margin-top: 40px;
                h3 {
                  font-size: 30px;
                }
                small {
                  margin-top: 10px;
                  text-align: left;
                }
              }
            }
          }
        }
      }
    }
  }
}