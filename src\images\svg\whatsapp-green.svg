var _style, _path, _path2;
function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
import * as React from "react";
function SvgWhatsappGreen(_ref, svgRef) {
  let {
    title,
    titleId,
    ...props
  } = _ref;
  return /*#__PURE__*/React.createElement("svg", _extends({
    id: "Icons",
    xmlns: "http://www.w3.org/2000/svg",
    xmlnsXlink: "http://www.w3.org/1999/xlink",
    viewBox: "0 0 32 32",
    xmlSpace: "preserve",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _style || (_style = /*#__PURE__*/React.createElement("style", {
    type: "text/css"
  }, "\r\n\t.st0{fill:#FFFFFF;}\r\n\t.st1{fill:#3A559F;}\r\n\t.st2{fill:#F4F4F4;}\r\n\t.st3{fill:#FF0084;}\r\n\t.st4{fill:#0063DB;}\r\n\t.st5{fill:#00ACED;}\r\n\t.st6{fill:#FFEC06;}\r\n\t.st7{fill:#FF0000;}\r\n\t.st8{fill:#25D366;}\r\n\t.st9{fill:#0088FF;}\r\n\t.st10{fill:#314358;}\r\n\t.st11{fill:#EE6996;}\r\n\t.st12{fill:#01AEF3;}\r\n\t.st13{fill:#FFFEFF;}\r\n\t.st14{fill:#F06A35;}\r\n\t.st15{fill:#00ADEF;}\r\n\t.st16{fill:#1769FF;}\r\n\t.st17{fill:#1AB7EA;}\r\n\t.st18{fill:#6001D1;}\r\n\t.st19{fill:#E41214;}\r\n\t.st20{fill:#05CE78;}\r\n\t.st21{fill:#7B519C;}\r\n\t.st22{fill:#FF4500;}\r\n\t.st23{fill:#00F076;}\r\n\t.st24{fill:#FFC900;}\r\n\t.st25{fill:#00D6FF;}\r\n\t.st26{fill:#FF3A44;}\r\n\t.st27{fill:#FF6A36;}\r\n\t.st28{fill:#0061FE;}\r\n\t.st29{fill:#F7981C;}\r\n\t.st30{fill:#EE1B22;}\r\n\t.st31{fill:#EF3561;}\r\n\t.st32{fill:none;stroke:#FFFFFF;stroke-width:2;stroke-miterlimit:10;}\r\n\t.st33{fill:#0097D3;}\r\n\t.st34{fill:#01308A;}\r\n\t.st35{fill:#019CDE;}\r\n\t.st36{fill:#FFD049;}\r\n\t.st37{fill:#16A05D;}\r\n\t.st38{fill:#4486F4;}\r\n\t.st39{fill:none;}\r\n\t.st40{fill:#34A853;}\r\n\t.st41{fill:#4285F4;}\r\n\t.st42{fill:#FBBC05;}\r\n\t.st43{fill:#EA4335;}\r\n")), _path || (_path = /*#__PURE__*/React.createElement("path", {
    className: "st8",
    d: "M17,0C8.7,0,2,6.7,2,15c0,3.4,1.1,6.6,3.2,9.2l-2.1,6.4c-0.1,0.4,0,0.8,0.3,1.1C3.5,31.9,3.8,32,4,32 c0.1,0,0.3,0,0.4-0.1l6.9-3.1C13.1,29.6,15,30,17,30c8.3,0,15-6.7,15-15S25.3,0,17,0z"
  })), _path2 || (_path2 = /*#__PURE__*/React.createElement("path", {
    className: "st0",
    d: "M25.7,20.5c-0.4,1.2-1.9,2.2-3.2,2.4C22.2,23,21.9,23,21.5,23c-0.8,0-2-0.2-4.1-1.1c-2.4-1-4.8-3.1-6.7-5.8 L10.7,16C10.1,15.1,9,13.4,9,11.6c0-2.2,1.1-3.3,1.5-3.8c0.5-0.5,1.2-0.8,2-0.8c0.2,0,0.3,0,0.5,0c0.7,0,1.2,0.2,1.7,1.2l0.4,0.8 c0.3,0.8,0.7,1.7,0.8,1.8c0.3,0.6,0.3,1.1,0,1.6c-0.1,0.3-0.3,0.5-0.5,0.7c-0.1,0.2-0.2,0.3-0.3,0.3c-0.1,0.1-0.1,0.1-0.2,0.2 c0.3,0.5,0.9,1.4,1.7,2.1c1.2,1.1,2.1,1.4,2.6,1.6l0,0c0.2-0.2,0.4-0.6,0.7-0.9l0.1-0.2c0.5-0.7,1.3-0.9,2.1-0.6 c0.4,0.2,2.6,1.2,2.6,1.2l0.2,0.1c0.3,0.2,0.7,0.3,0.9,0.7C26.2,18.5,25.9,19.8,25.7,20.5z"
  })));
}
const ForwardRef = /*#__PURE__*/React.forwardRef(SvgWhatsappGreen);
export default __webpack_public_path__ + "static/media/whatsapp-green.bc8eeb10aad7ab8ef44005f1e5ab26f0.svg";
export { ForwardRef as ReactComponent };