import React, { useEffect, useState } from "react";
import {useDispatch, useSelector} from 'react-redux'
import Sidebar from "@components/admin/Sidebar";
import { createCoupon } from "@redux/actions/couponAction";
import { useNavigate } from "react-router-dom";
import { getLoggedUser } from "@redux/actions/authAction";

const AddCoupon = () => {
  const [couponName, setCouponName] = useState("");
  const [discount, setDiscount] = useState("");
  const [date, setDate] = useState("");

  const dispatch = useDispatch()
  const couponData = useSelector(state=> state.couponReducer.coupon)
  const loggedUserData = useSelector(state=>state.authReducer.getMe)

  const navigate = useNavigate()

  useEffect(()=>{
    if(couponData){
        if(couponData.status === 201){
          setCouponName("")
          setDiscount("")
          setDate("")
        }
    }
  },[couponData])

  const handelSubmit = (e)=>{
    e.preventDefault()
    dispatch(createCoupon({
        name: couponName,
        expire: date,
        discount: discount
    }))
  }


  useEffect(()=>{
    dispatch(getLoggedUser())
  },[])


  useEffect(()=>{
    if(loggedUserData){
      if(loggedUserData.status === 200){
        if(loggedUserData.data){
          if(loggedUserData.data.data.role !== "admin"){
            navigate('/shop')
          }
        }
      }else if(loggedUserData.status === 401){
        navigate('/shop')
      }
    }
  },[loggedUserData])


  return (
    <div className="admin-page">
    <div className="container">
      <div className="admin-page-wraper">
        <Sidebar/>
        <section className="dashboard-container">
          <h3>ADD NEW COUPON</h3>
          <div className="add-wraper">
            <form>
              <input type="text" placeholder="Coupon Name" value={couponName} onChange={(e)=> setCouponName(e.target.value)}/>
              <input type="number" placeholder="Coupon Discount" value={discount} onChange={(e)=> setDiscount(e.target.value)}/>
              <input type="date" value={date} onChange={(e)=> setDate(e.target.value)}/>
              <input className="submit" type="submit" value="Create Coupon" onClick={(e)=> handelSubmit(e)}/>
            </form>
          </div>
        </section>
      </div>
    </div>
  </div>
  )
}

export default AddCoupon