
import "@styles/main.scss";
import { Routes, Route, BrowserRouter } from "react-router-dom";
import ScrollToTop from "./utilities/ScrollToTop";

import Home from "@/pages/home/<USER>";
import HomeAr from "@/pages/home/<USER>";
import AboutUs from "@/pages/home/<USER>";
import AboutUsAr from "@/pages/home/<USER>";
import Molasses from "@/pages/home/<USER>";
import Tahini from "@/pages/home/<USER>";
import MolassesAr from "@/pages/home/<USER>";
import TahiniAr from "@/pages/home/<USER>";
import MolassesArEd from "@/pages/home/<USER>";
import TahiniArEd from "@/pages/home/<USER>";
import Mix from "@/pages/home/<USER>";
import MixAr from "@/pages/home/<USER>";
//import asal from "@/pages/home/<USER>";
import Contacts from "@/pages/home/<USER>";

import SingleBlog from "@/pages/blog/SingleBlog";
import Blog1 from "@/pages/blog/Blog1";
import Blog2 from "@/pages/blog/Blog2";
import Blog3 from "@/pages/blog/Blog3";
import Blog4 from "@/pages/blog/Blog4";
import Blog5 from "@/pages/blog/Blog5";
import Blog6 from "@/pages/blog/Blog6";
import Blog7 from "@/pages/blog/Blog7";
import Blog8 from "@/pages/blog/Blog8";
import Blog9 from "@/pages/blog/Blog9";
import Blog10 from "@/pages/blog/Blog10";
import Blog11 from "@/pages/blog/Blog11";
import Blog12 from "@/pages/blog/Blog12";
import Blog13 from "@/pages/blog/Blog13";
import Blog14 from "@/pages/blog/Blog14";
import Blogs from "@/pages/blog/Blogs";
import AllArticles from "@/pages/blog/AllArticles";

import Shop from "@/pages/shop/Shop";
import SingleProduct from "@/pages/shop/SingleProduct";

import Login from "@pages/shop/account/Login";
import Signup from "@pages/shop/account/Signup";
import Profile from "./pages/shop/account/Profile";
import AddAddress from "./pages/shop/account/AddAddress";

import Cart from "@pages/shop/order/Cart";
import Checkout from "@pages/shop/order/Checkout";

import ManageProducts from "@pages/admin/ManageProducts";
import AddCategory from "@pages/admin/AddCategory";
import AddBrand from "@pages/admin/AddBrand";
import AddProduct from "@pages/admin/AddProduct";
import EditProduct from "@pages/admin/EditProduct";
import AddCoupon from "@pages/admin/AddCoupon";
import ManageCoupons from "@pages/admin/ManageCoupons";
import ManageOrders from "@pages/admin/ManageOrders";
import ManageUsers from "@pages/admin/ManageUsers";
import OrderDetails from "@pages/shop/order/OrderDetails";
import UserDetails from "@pages/admin/UserDetails";
import ForgetPassword from "@pages/shop/account/ForgetPassword";
import ResetCode from "@pages/shop/account/ResetCode";
import ResetPassword from "@pages/shop/account/ResetPassword";



function App() {
  return (
    <BrowserRouter>
      <ScrollToTop>
        <main className="App">
          <Routes>
            <Route path="/" element={<Home />} />
            <Route exact path="ar" element={<HomeAr />} />

            <Route path="about" element={<AboutUs />} />
            <Route path="ar/about" element={<AboutUsAr />} />

            <Route path="contacts" element={<Contacts />} />

            <Route path="molasses" element={<Molasses />} />
            <Route path="ar/molasses" element={<MolassesAr />} />
            <Route path="ar/molassesEd" element={<MolassesArEd />} />


            <Route path="tahini" element={<Tahini />} />
			<Route path="ar/tahiniEd" element={<TahiniArEd />} />
            <Route path="ar/tahini" element={<TahiniAr />} />
			<Route path="SuperUniverse" element={<Mix />} />
			<Route path="ar/SuperUniverse" element={<MixAr />} />
			<Route path="ar/asal" element={<asal />} />

            <Route path="shop" element={<Shop />} />

           

            <Route path="login" element={<Login />} />
            <Route path="signup" element={<Signup />} />
            <Route path="profile" element={<Profile />} />
            <Route path="forget-password" element={<ForgetPassword />} />
            <Route path="reset-code" element={<ResetCode />} />
            <Route path="reset-password" element={<ResetPassword />} />
            <Route path="add-address" element={<AddAddress />} />

            <Route path="admin" element={<ManageProducts />} />
            <Route path="admin/manage-categories" element={<AddCategory />} />
            <Route path="admin/manage-brands" element={<AddBrand />} />
            <Route path="admin/add-product" element={<AddProduct />} />
            <Route path="admin/edit-product/:id" element={<EditProduct />} />
            <Route path="admin/add-coupon" element={<AddCoupon />} />
            <Route path="admin/manage-coupons" element={<ManageCoupons />} />
            <Route path="admin/manage-orders" element={<ManageOrders />} />
            <Route path="admin/order-details/:id" element={<OrderDetails />} />
            <Route path="admin/manage-users" element={<ManageUsers />} />
            <Route path="admin/user-details/:id" element={<UserDetails />} />

            <Route path="cart" element={<Cart />} />
            <Route path="checkout" element={<Checkout />} />

            <Route path="shop/product/:id" element={<SingleProduct />} />
            <Route path="blog1" element={<Blog1 />} />
            <Route path="blog2" element={<Blog2 />} />
            <Route path="blog3" element={<Blog3 />} />
            <Route path="blog4" element={<Blog4 />} />
            <Route path="blog5" element={<Blog5 />} />
            <Route path="blog6" element={<Blog6 />} />
            <Route path="blog7" element={<Blog7 />} />
            <Route path="blog8" element={<Blog8 />} />
            <Route path="blog9" element={<Blog9 />} />
            <Route path="blog10" element={<Blog10 />} />
            <Route path="blog11" element={<Blog11 />} />
            <Route path="blog12" element={<Blog12 />} />
            <Route path="blog13" element={<Blog13 />} />
            <Route path="blog14" element={<Blog14 />} />
            <Route path="blogs" element={<Blogs />} />

            <Route path="blogs/all-articles" element={<AllArticles />} />
            <Route path="blogs/blog" element={<SingleBlog />} />

          </Routes>
        </main>
      </ScrollToTop>
    </BrowserRouter>
  );
}

export default App;