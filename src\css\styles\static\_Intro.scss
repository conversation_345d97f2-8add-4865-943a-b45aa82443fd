@use "sass:map";
@use '../responsive/_responsive-helpers.scss' as *;


// الخلفية والسوشيال ميديا

@keyframes zoomInFromCenter {
  0% {
    opacity: 0;
    transform: scale(0);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.intro {
  position: relative;
  z-index: 100; // جعل الانترو طبقة أعلى بوضوح فوق info
  background: url("@images/imgs/bg-body4.jpg");
  background-color: #000;
  padding-top: 40px;
  width: 100%;
  overflow-x: hidden; // منع تمرير أفقي
  overflow-y: visible; // السماح ببروز الصورة خارج حدود الانترو

  .social-media-links {
    display: flex;
    justify-content: center;
    position: absolute;
    right: -240px;
    top: 53%;
    transform: rotate(-90deg);
    width: 40%;
    z-index: 99;

    .social {
      margin-right: 70px;
      width: 40px;

      &:last-of-type {
        margin-right: 0;
      }

      a {
        color: #fff;
        display: inline-block;
        font-size: 12px;
        font-weight: 400;

        p {
          display: none;
        }

        &:hover {
          color: $mainColor;

          span {
            display: none;
          }

          p {
            display: block;
          }
        }
      }
    }
  }

  // السلايدر
  .swiper {
    position: relative;
    z-index: 2;
    height: auto; // لا نستخدم fit-content لتفادي سكرول داخلي
    width: 100vw;
    max-width: 100vw;
    overflow: visible; // السماح ببروز الصورة خارج حدود السويبر

    .swiper-slide {
      height: auto; // منع ارتفاع داخلي يتسبب في سكرول
      width: 100vw;
      object-fit: contain;
      &.swiper-slide-active:first-child {
      .container {
        .info-wraper {
          animation: zoomInFromCenter 3s ease-out forwards;
        }

        .slide-img {
          animation: zoomInFromCenter 2s ease-out forwards;
          /* تأخير بسيط للصورة */
        }
      }
    }

      &.full {
        background-size: cover;
        width: 100vw;
        height: auto;
        display: flex;
        justify-content: center;
        align-items: flex-start;
        padding-top: 0;

        .slide-img {
          width: 100vw;
          height: auto;
          img {
            display: block;
            position: relative;
            margin-top: -40px; // التحام مع أعلى الشاشة (إلغاء padding-top للحاوية .intro)
            width: 100vw;
            height: auto;
            max-height: 90vh; // أقصى ارتفاع
            object-fit: cover;
          }
        }
      }
    }

      .container {
        display: flex;
        object-fit: contain;
        flex-wrap: nowrap;
        flex-direction: row;
        justify-items: center;
        align-self: center;
        justify-content: space-between;
        gap: 24px;
        min-height: 0; // يمنع تمدّد غير ضروري

        .info-wraper {
          align-self: center;
          align-items: center;
          position: relative;
          width: 100%;

          h3 {
            color: $mainColor;
            font-size: 14px;
            font-weight: 600;
            letter-spacing: 1px;
            padding-left: 25px;
            position: relative;
            text-transform: uppercase;
            width: 100%;

            &::before {
              background-color: $mainColor;
              content: "";
              height: 1px;
              left: 0;
              position: absolute;
              top: 50%;
              transform: translateY(-50%);
              width: 15px;
            }
          }

          p {
            color: #fbfbef;
            font-size: 48px;
            font-weight: 600;
            line-height: 1.3;
            margin-top: 20px;

            span {
              color: $mainColor;
              display: inline-block;
              margin-left: 10px;
            }
          }

          .work-with {
            align-items: center;
            border: 1px solid transparent;
            display: flex;
            height: fit-content;
            justify-content: center;
            margin-top: 40px;
            padding: 30px 0;
            position: relative;
            width: fit-content;

            a {
              color: #fff;
              font-size: 12px;
              font-weight: 600;
              letter-spacing: 1.5px;
              padding: 0 30px;
              position: relative;
              text-transform: uppercase;
              transition: all ease-in-out 0.5s;

              &::before {
                background-color: $mainColor;
                content: "";
                height: 15px;
                left: 0;
                position: absolute;
                top: -15px;
                transition: all ease-in-out 0.5s;
                width: 1px;
              }

              &::after {
                background-color: $mainColor;
                bottom: -15px;
                content: "";
                height: 20px;
                position: absolute;
                right: 0;
                transition: all ease-in-out 0.5s;
                width: 1px;
              }
            }

            &::before {
              background-color: $mainColor;
              content: "";
              height: 1px;
              position: absolute;
              right: 0;
              top: 5px;
              transition: all ease-in-out 0.5s;
              width: 80px;
            }

            &::after {
              background-color: $mainColor;
              bottom: 5px;
              content: "";
              height: 1px;
              left: 0;
              position: absolute;
              transition: all ease-in-out 0.5s;
              width: 40px;
            }

            &:hover {

              a::before,
              a::after {
                height: 66px;
              }

              a::before {
                top: -24px;
              }

              a::after {
                bottom: -24px;
              }

              &::before,
              &::after {
                width: 168px;
              }
            }
          }

          &.ar {
            font-family: 'Noto Kufi Arabic', Arial, sans-serif;
            text-align: right;

            h3 {
              padding-left: 0;
              padding-right: 25px;
              text-align: right;

              &::before {
                left: unset;
                right: 0;
              }
            }

            p {
              line-height: 1.6;
              word-spacing: -8px;

              span {
                margin-left: unset;
                margin-right: 10px;
              }
            }

            .work-with {
              margin-left: auto;

              &::before {
                background-color: $mainColor;
                bottom: 5px;
                content: "";
                height: 1px;
                position: absolute;
                right: 0;
                transition: all ease-in-out 0.5s;
                width: 80px;
              }

              &::after {
                background-color: $mainColor;
                bottom: 5px;
                content: "";
                height: 1px;
                position: absolute;
                right: 0;
                transition: all ease-in-out 0.5s;
                width: 40px;
              }

              &:hover {

                a::before,
                a::after {
                  height: 70px;
                }

                a::before {
                  top: -24px;
                }

                a::after {
                  bottom: -24px;
                }

                &::before,
                &::after {
                  width: 128px;
                }
              }
            }
          }
        }

        .slide-img {
          position: relative;
          right: -3%;
          align-self: center;
          animation: zoomInFromCenter 0.5s ease-out forwards;
          transform-origin: center right;
          /* لضمان التكبير من المنتصف */
          animation-delay: 0.3s;
          
          img {
            display: block;
            width: auto;
            height: auto;
            max-height: 88vh; // أقل من 90vh لتفادي ظهور سكرول داخلي
            transform: translateY(0%);
            margin-top: -12px; // بروز علوي بسيط
            margin-bottom: -32px; // بروز سفلي فوق مكوّن info
          }

        }
      }
    }

    

    .swiper-pagination {
      bottom: 40%;
      left: 7.5%;

      .swiper-pagination-bullet {
        background-color: #fbfbef;
        border-radius: 0;
        height: 1px;
        margin: 0;
        position: relative;
        width: 50px;

        &.swiper-pagination-bullet-active {
          background-color: $mainColor;
        }

        &::before {
          color: #fbfbef;
          content: "1";
          font-size: 12px;
          font-weight: 600;
          position: absolute;
          top: -30px;
        }

        &:nth-of-type(2)::before {
          content: "2";
        }

        &:nth-of-type(3)::before {
          content: "3";
        }
      }
    }

    &.ar {
      .swiper-pagination {
        left: unset;
        right: 7.5%;
        bottom: 10%;
      }

      .swiper-slide {
        .container {
          direction: rtl;

          .info-wraper {
            text-align: right;

            h3 {
              font-family: 'Noto Kufi Arabic', Arial, sans-serif;
              font-size: 50px;
              line-height: 1.2;
            }

            p {
              font-family: 'Noto Kufi Arabic', Arial, sans-serif;
              font-size: 40px;
              line-height: 1.4;
              text-align: right;

              span {
                font-family: 'Noto Kufi Arabic', Arial, sans-serif;
              }
            }

            .work-with {
              margin-left: unset;
              margin-right: auto;

              a {
                font-family: 'Noto Kufi Arabic', Arial, sans-serif;
              }

              &::before,
              &::after {
                left: 0;
                right: unset;
              }
            }
          }
        }
      }
    }
  }

  // السماح ببروز العناصر خارج غلاف السويبر (الـ wrapper)
  .swiper-wrapper { 
    overflow: visible; 
    height: auto; 
  }

  // إبراز صورة السلايد الأول فوق عناصر المعلومات
  .swiper {
    .swiper-slide:first-child {
      overflow: visible !important; // إجبار السماح بظهور بروز الصورة خارج حدود السويبر
      max-height: 85vh !important; // حد أقصى يمنع ظهور شريط سكرول داخلي
      height: auto !important; // منع ارتفاع ثابت

      .container { 
        position: relative; 
        height: auto;
        overflow: visible;
      }
      .info-wraper { 
        position: relative; 
        z-index: 1; 
      }
      .slide-img { 
        position: relative; 
        z-index: 2; 
        pointer-events: none;
        overflow: visible;
        
        img {
          display: block;
          width: auto;
          height: auto;
          max-height: 82vh !important; // أقل من 85vh لتفادي ظهور سكرول داخلي
          transform: translateY(0%);
          margin-top: -12px; // بروز علوي بسيط
          margin-bottom: -48px !important; // بروز سفلي أكبر فوق مكوّن info
          overflow: visible;
        }
      }
    }
  }





/*
  ============== تخطيطات السلايدر حسب الاتجاه ==============
  - سلايد 1 (لاندسكيب): الصورة يمين وتبرز رأسياً قليلاً، النص يسارها
  - سلايد 1 (بورتريه): نصف الصورة خارج يمين الشاشة، النص يسارها
  - سلايد 2 و3: صور كاملة العرض بارتفاعها الطبيعي وتلتحم مع النافبار والحافة السفلية
*/



// سلايد 1 - بورتريه
@media (orientation: portrait) {
  .intro {
    .swiper {
      .swiper-slide:first-child {
        overflow: visible; // السماح ببروز الصورة أسفل السلايد
        .container {
          flex-direction: row;
          align-items: center;
          .info-wraper { width: 55%; }
          .slide-img {
            width: 45%;
            transform: translateX(45%);
            img { width: auto; height: auto; max-height: 70vh; object-fit: contain; }
          }
        }
      }
    }
  }
}

// السلايد 2 و3 (كامل العرض) — إلغاء قيود الارتفاع ونقل الـ pagination لأسفل
.intro {
  .swiper.is-full {
    max-height: 90vh; // لا نتجاوز 90vh لمنع السكرول
    overflow: hidden; // منع السكرول
    margin-top: -40px; // التحام مع أعلى الشاشة
    padding-bottom: 0;
    .swiper-wrapper { height: auto; }
    .swiper-slide.full { height: auto; }
    .swiper-pagination {
      bottom: 12px;
      left: 50%;
      transform: translateX(-50%);
    }
  }

  // عندما لا يكون السلايد كامل (سلايد 1)، نخلي الانترو فوق مكون info الذي يليه
  &.is-overlap {
    z-index: 100;
    pointer-events: auto;
  }
}

// إلغاء إزاحة الصورة لأعلى عندما يكون السلايد كامل العرض وتم إزاحة السويبر نفسه لأعلى
.intro {
  .swiper.is-full {
    .swiper-slide.full {
      .slide-img img { margin-top: 0; }
    }
  }
}

// ========== تنسيقات قوية لإجبار البروز وإزالة السكرول ==========
// إجبار إزالة السكرول الداخلي للسلايد الأول
.intro .swiper .swiper-slide:first-child {
  overflow: visible !important;
  max-height: 75vh !important;
  height: auto !important;
  
  .container {
    overflow: visible !important;
    height: auto !important;
  }
  
  .slide-img {
    overflow: visible !important;
    
    img {
      max-height: 72vh !important;
      margin-bottom: -80px !important;
      overflow: visible !important;
    }
  }
}

// إجبار البروز فوق مكون info
.intro.is-overlap + .info {
  margin-top: -120px !important;
  padding-top: calc(130px + 120px) !important;
  z-index: 1 !important;
}

// منع أي سكرول داخلي
.intro .swiper,
.intro .swiper .swiper-wrapper,
.intro .swiper .swiper-slide:first-child,
.intro .swiper .swiper-slide:first-child .container,
.intro .swiper .swiper-slide:first-child .slide-img {
  overflow: visible !important;
}

// إجبار z-index للانترو
.intro.is-overlap {
  z-index: 50 !important; // تقليل z-index لعدم إخفاء النافبار
  position: relative !important;
}

// ========== تنسيقات إضافية لضمان البروز ==========
// إجبار الصورة أن تبرز فوق كل شيء
.intro.is-overlap .swiper .swiper-slide:first-child .slide-img img {
  position: relative !important;
  z-index: 51 !important; // أعلى من الانترو بقليل
  margin-bottom: -100px !important;
}

// إجبار info أن يكون تحت الصورة
.intro.is-overlap + .info {
  position: relative !important;
  z-index: 1 !important;
  margin-top: -140px !important;
  padding-top: calc(130px + 140px) !important;
}

// منع أي overflow يمنع البروز
* {
  &.intro,
  &.swiper,
  &.swiper-wrapper,
  &.swiper-slide:first-child,
  &.container,
  &.slide-img {
    overflow: visible !important;
  }
}

// ========== ضمان عدم إخفاء النافبار ==========
// النافبار يجب أن يكون أعلى من كل شيء
.navbar {
  z-index: 999 !important;
  position: fixed !important;
}

// الانترو يجب أن يكون تحت النافبار
.intro.is-overlap {
  z-index: 50 !important;
  position: relative !important;
}

// ========== إصلاح تنسيقات الموبايل والبورتريه ==========
// سلايد 1 - بورتريه (إصلاح تداخل الصور)
@media (orientation: portrait) {
  .intro {
    .swiper {
      .swiper-slide:first-child {
        overflow: visible !important;
        max-height: 75vh !important;
        
        .container {
          flex-direction: row !important;
          align-items: center !important;
          gap: 20px !important;
          
          .info-wraper { 
            width: 50% !important; 
            flex-shrink: 0 !important;
          }
          
          .slide-img {
            width: 50% !important;
            flex-shrink: 0 !important;
            transform: translateX(25%) !important; // تقليل الإزاحة لمنع التداخل
            
            img { 
              width: auto !important; 
              height: auto !important; 
              max-height: 65vh !important; 
              object-fit: contain !important;
              margin-bottom: -60px !important; // تقليل البروز في الموبايل
            }
          }
        }
      }
      
      // السلايد 2 و3 في البورتريه
      .swiper-slide.full {
        .slide-img img {
          width: 100vw !important;
          height: auto !important;
          max-height: 85vh !important;
          object-fit: cover !important;
        }
      }
    }
  }
}