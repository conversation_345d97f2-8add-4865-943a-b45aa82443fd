var _g;
function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
import * as React from "react";
function SvgInnovation(_ref, svgRef) {
  let {
    title,
    titleId,
    ...props
  } = _ref;
  return /*#__PURE__*/React.createElement("svg", _extends({
    fill: "#8f6B29",
    id: "Capa_1",
    xmlns: "http://www.w3.org/2000/svg",
    xmlnsXlink: "http://www.w3.org/1999/xlink",
    width: "800px",
    height: "800px",
    viewBox: "0 0 571.2 571.2",
    xmlSpace: "preserve",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _g || (_g = /*#__PURE__*/React.createElement("g", null, /*#__PURE__*/React.createElement("path", {
    d: "M353.601,496.4c0,7.507-6.093,13.6-13.601,13.6H231.2c-7.507,0-13.6-6.093-13.6-13.6c0-7.508,6.093-13.601,13.6-13.601H340 C347.508,482.8,353.601,488.893,353.601,496.4z M340,516.8H231.2c-8.949,0-15.878,8.644-12.899,18.034 c1.795,5.664,7.527,9.166,13.471,9.166h0.204c7.854,0,15.035,4.44,18.55,11.465l0.143,0.286 c4.74,9.465,14.416,15.449,25.004,15.449h19.856c10.588,0,20.264-5.984,24.997-15.449l0.143-0.286 c3.516-7.024,10.696-11.465,18.55-11.465h0.204c5.943,0,11.676-3.502,13.471-9.166C355.878,525.443,348.949,516.8,340,516.8z  M285.601,81.6c7.507,0,13.6-6.093,13.6-13.6V13.6c0-7.507-6.093-13.6-13.6-13.6C278.093,0,272,6.093,272,13.6V68 C272,75.507,278.093,81.6,285.601,81.6z M141.352,133.382c2.652,2.659,6.134,3.985,9.615,3.985c3.482,0,6.963-1.326,9.615-3.985 c5.311-5.311,5.311-13.92,0-19.23l-38.467-38.468c-5.304-5.311-13.927-5.311-19.23,0c-5.311,5.311-5.311,13.919,0,19.23 L141.352,133.382z M108.8,258.4c0-7.507-6.093-13.6-13.6-13.6H40.8c-7.507,0-13.6,6.093-13.6,13.6c0,7.507,6.093,13.6,13.6,13.6 h54.4C102.708,272,108.8,265.907,108.8,258.4z M141.352,383.418l-38.467,38.468c-5.311,5.311-5.311,13.92,0,19.23 c2.652,2.659,6.133,3.984,9.615,3.984c3.481,0,6.963-1.325,9.615-3.984l38.467-38.468c5.311-5.311,5.311-13.919,0-19.23 C155.278,378.107,146.656,378.107,141.352,383.418z M429.849,383.418c-5.311-5.311-13.92-5.311-19.23,0s-5.311,13.92,0,19.23 l38.468,38.468c2.658,2.659,6.134,3.984,9.615,3.984s6.956-1.325,9.615-3.984c5.311-5.311,5.311-13.92,0-19.23L429.849,383.418z  M530.4,244.8H476c-7.507,0-13.6,6.093-13.6,13.6c0,7.507,6.093,13.6,13.6,13.6h54.4c7.507,0,13.6-6.093,13.6-13.6 C544,250.893,537.907,244.8,530.4,244.8z M420.233,137.367c3.481,0,6.956-1.326,9.615-3.985l38.468-38.468 c5.311-5.311,5.311-13.919,0-19.23c-5.311-5.311-13.92-5.311-19.23,0l-38.468,38.468c-5.311,5.311-5.311,13.919,0,19.23 C413.271,136.041,416.752,137.367,420.233,137.367z M353.601,462.4c0,7.507-6.093,13.6-13.601,13.6H231.2 c-7.507,0-13.6-6.093-13.6-13.6c0-7.242,5.678-13.11,12.818-13.519C221.952,372.354,142.8,355.307,142.8,265.2 c0-78.866,63.934-142.8,142.8-142.8c78.866,0,142.8,63.934,142.8,142.8c0,90.106-79.152,107.154-87.618,183.682 C347.922,449.29,353.601,455.158,353.601,462.4z M254.259,160.548c-2.115-5.216-8.051-7.725-13.287-5.624 c-34.755,14.083-61.104,44.186-70.482,80.525c-1.408,5.46,1.877,11.016,7.331,12.424c0.85,0.224,1.707,0.326,2.55,0.326 c4.542,0,8.684-3.053,9.874-7.65c7.766-30.11,29.594-55.053,58.385-66.715C253.851,171.721,256.367,165.777,254.259,160.548z"
  }))));
}
const ForwardRef = /*#__PURE__*/React.forwardRef(SvgInnovation);
export default __webpack_public_path__ + "static/media/innovation.a7c4671b0ac7c0afff09490b062f2c4b.svg";
export { ForwardRef as ReactComponent };