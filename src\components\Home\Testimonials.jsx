import React from "react";
// Import Swiper React components
import { Swiper, SwiperSlide } from "swiper/react";

// Import Swiper styles
import "swiper/css";
import Part1 from "@images/imgs/s.webp";
import Part2 from "@images/imgs/sl.webp";
import Part3 from "@images/imgs/lt.png";
import Part4 from "@images/imgs/z.webp";
import Part5 from "@images/imgs/m.webp";
import Part6 from "@images/imgs/p.webp";

// Import Autoplay module
import { Autoplay } from "swiper/modules";

const Testimonials = () => {
  const imagesList = [Part1, Part2, Part3, Part4, Part5, Part6];

  return (
    <section className="testimonials">
      <div className="container">
        <div className="testimonials-container">
          <div className="title">
            <h5>Success stories</h5>
            <h3>Success partners</h3>
          </div>
          <div className="testimonials-wraper">
            <div className="testimonial">
              <div className="testimonial-details">
                <p>
                  Adam for Foodstuffs has been privileged to contribute to the
                  building and strengthening of many global brands. Through
                  mutual cooperation, these brands have gained the favor of
                  their audiences, achieved broad and sustainable successes, and
                  have been the fastest growing.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="parteners">
        <Swiper
          className="mySwiper"
          slidesPerView={7}
          spaceBetween={20}
          autoplay={{
            delay: 2500,
            disableOnInteraction: false,
          }}
          modules={[Autoplay]}
          breakpoints={{
            350: {
              slidesPerView: 3,
              spaceBetween: 10,
            },
            768: {
              slidesPerView: 4,
              spaceBetween: 40,
            },
            1024: {
              slidesPerView: 7,
              spaceBetween: 40,
            },
          }}
        >
          {imagesList.map((img, index) => (
            <SwiperSlide key={index}>
              <img loading="lazy" src={img} alt="" />
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </section>
  );
};

export default Testimonials;