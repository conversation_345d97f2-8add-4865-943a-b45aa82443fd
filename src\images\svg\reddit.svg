var _g;
function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
import * as React from "react";
function SvgReddit(_ref, svgRef) {
  let {
    title,
    titleId,
    ...props
  } = _ref;
  return /*#__PURE__*/React.createElement("svg", _extends({
    height: 512,
    viewBox: "0 0 176 176",
    width: 512,
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _g || (_g = /*#__PURE__*/React.createElement("g", {
    id: "Layer_2",
    "data-name": "Layer 2"
  }, /*#__PURE__*/React.createElement("g", {
    id: "reddit"
  }, /*#__PURE__*/React.createElement("rect", {
    id: "background",
    fill: "#ff4500",
    height: 176,
    rx: 24,
    width: 176
  }), /*#__PURE__*/React.createElement("path", {
    id: "icon",
    d: "m130.74 75.66a12.27 12.27 0 0 0 -8.74 3.74c-8.28-5.81-19.43-9.55-31.79-10l6.42-29.36 20.37 4.71a9.16 9.16 0 0 0 9.12 9.25 9.34 9.34 0 1 0 -8.18-13.5l-22.58-5.11a2.17 2.17 0 0 0 -2.56 1.67l-7 32.37c-12.26.52-23.3 4.25-31.6 10.07a12.22 12.22 0 0 0 -8.9-3.84c-12.89 0-17.11 17.55-5.31 23.55a27.21 27.21 0 0 0 -.6 5.81c0 19.72 21.88 35.69 48.76 35.69s48.85-15.97 48.85-35.71a22.89 22.89 0 0 0 -.72-5.91c11.57-6 7.31-23.44-5.51-23.45zm-72.07 24.8a9.15 9.15 0 1 1 9.2 9.22 9.23 9.23 0 0 1 -9.2-9.22zm49.68 22c-8.44 8.56-32.25 8.56-40.69 0a2.22 2.22 0 1 1 3.06-3.22c6.44 6.7 27.82 6.82 34.55 0a2.23 2.23 0 0 1 3.08 3.22zm-.19-12.75a9.28 9.28 0 1 1 9.21-9.22 9.17 9.17 0 0 1 -9.21 9.21z",
    fill: "#fff"
  })))));
}
const ForwardRef = /*#__PURE__*/React.forwardRef(SvgReddit);
export default __webpack_public_path__ + "static/media/reddit.fa40bfbe0384ecccdda094b561c6a4ba.svg";
export { ForwardRef as ReactComponent };