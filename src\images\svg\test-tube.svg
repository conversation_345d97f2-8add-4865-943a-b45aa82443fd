var _path;
function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
import * as React from "react";
function SvgTestTube(_ref, svgRef) {
  let {
    title,
    titleId,
    ...props
  } = _ref;
  return /*#__PURE__*/React.createElement("svg", _extends({
    fill: "#1B1819",
    width: "800px",
    height: "800px",
    viewBox: "0 0 256 256",
    id: "Flat",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    d: "M237.65674,86.34326l-60-60a8.00063,8.00063,0,0,0-11.314,0L62.39453,130.29193l-.01172.01129-26.04,26.04a44.76956,44.76956,0,0,0,63.314,63.31348L177.605,141.70868l.0127-.01288,34.70361-34.70361,22.2085-7.40283a7.9996,7.9996,0,0,0,3.127-13.2461Zm-32.18653,6.06738a8.00221,8.00221,0,0,0-3.12744,1.93262l-35.59814,35.59821c-2.148,1.51983-17.895,11.53961-43.167-1.09674-10.99854-5.49964-20.811-7.63062-29.18116-7.92743L172,43.314l45.19043,45.19Z"
  })));
}
const ForwardRef = /*#__PURE__*/React.forwardRef(SvgTestTube);
export default __webpack_public_path__ + "static/media/test-tube.3339168b617ffe8926211137b3986f4e.svg";
export { ForwardRef as ReactComponent };