var _g;
function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
import * as React from "react";
function SvgLinkedin(_ref, svgRef) {
  let {
    title,
    titleId,
    ...props
  } = _ref;
  return /*#__PURE__*/React.createElement("svg", _extends({
    height: 512,
    viewBox: "0 0 176 176",
    width: 512,
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _g || (_g = /*#__PURE__*/React.createElement("g", {
    id: "Layer_2",
    "data-name": "Layer 2"
  }, /*#__PURE__*/React.createElement("g", {
    id: "linkedin"
  }, /*#__PURE__*/React.createElement("rect", {
    id: "background",
    fill: "#0077b5",
    height: 176,
    rx: 24,
    width: 176
  }), /*#__PURE__*/React.createElement("g", {
    id: "icon",
    fill: "#fff"
  }, /*#__PURE__*/React.createElement("path", {
    d: "m63.4 48a15 15 0 1 1 -15-15 15 15 0 0 1 15 15z"
  }), /*#__PURE__*/React.createElement("path", {
    d: "m60 73v66.27a3.71 3.71 0 0 1 -3.71 3.73h-15.81a3.71 3.71 0 0 1 -3.72-3.72v-66.28a3.72 3.72 0 0 1 3.72-3.72h15.81a3.72 3.72 0 0 1 3.71 3.72z"
  }), /*#__PURE__*/React.createElement("path", {
    d: "m142.64 107.5v32.08a3.41 3.41 0 0 1 -3.42 3.42h-17a3.41 3.41 0 0 1 -3.42-3.42v-31.09c0-4.64 1.36-20.32-12.13-20.32-10.45 0-12.58 10.73-13 15.55v35.86a3.42 3.42 0 0 1 -3.37 3.42h-16.42a3.41 3.41 0 0 1 -3.41-3.42v-66.87a3.41 3.41 0 0 1 3.41-3.42h16.42a3.42 3.42 0 0 1 3.42 3.42v5.78c3.88-5.82 9.63-10.31 21.9-10.31 27.18 0 27.02 25.38 27.02 39.32z"
  }))))));
}
const ForwardRef = /*#__PURE__*/React.forwardRef(SvgLinkedin);
export default __webpack_public_path__ + "static/media/linkedin.4144e29aefe363d5cc377ae314819d11.svg";
export { ForwardRef as ReactComponent };