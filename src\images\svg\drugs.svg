var _g;
function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
import * as React from "react";
function SvgDrugs(_ref, svgRef) {
  let {
    title,
    titleId,
    ...props
  } = _ref;
  return /*#__PURE__*/React.createElement("svg", _extends({
    fill: "#1B1819",
    height: "800px",
    width: "800px",
    id: "Layer_1",
    xmlns: "http://www.w3.org/2000/svg",
    xmlnsXlink: "http://www.w3.org/1999/xlink",
    viewBox: "0 0 512 512",
    xmlSpace: "preserve",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _g || (_g = /*#__PURE__*/React.createElement("g", null, /*#__PURE__*/React.createElement("g", null, /*#__PURE__*/React.createElement("path", {
    d: "M398.397,213.687l12.142-12.142c43.432-43.434,43.446-113.712,0.031-157.13c-21.028-21.025-48.98-32.226-78.717-32.226 c-0.013,0-0.03,0-0.042,0c-29.747,0-57.719,11.229-78.763,32.272L32.678,264.646C11.603,285.72-0.001,313.609,0,343.351 c0.003,29.723,11.591,57.732,32.631,78.771c21.033,21.035,48.991,32.688,78.722,32.688c0.014,0,0.024,0,0.039,0 c29.714,0,57.651-11.669,78.659-32.677l35.849-35.9c14.293,64.89,72.221,113.578,141.343,113.578 c79.819,0,144.756-64.95,144.756-144.769C512,285.92,463.288,227.979,398.397,213.687z M268.798,60.209 c16.839-16.84,39.22-25.75,63.022-25.75c0.012,0,0.022,0,0.034,0c23.787,0,46.15,8.886,62.968,25.704 c34.733,34.732,34.72,91.072-0.031,125.822l-24.554,24.459c-0.999-0.021-1.99-0.122-2.993-0.122 c-44.395,0-84.167,20.083-110.742,51.648l-94.908-94.731L268.798,60.209z M222.487,355.033c0,1.005,0.056,2.043,0.076,3.042 l-48.261,48.308c-16.803,16.805-39.144,26.156-62.913,26.156c-0.009,0-0.02,0-0.031,0c-23.784,0-46.151-9.382-62.98-26.212 c-16.835-16.832-26.106-39.247-26.109-63.021c-0.001-23.793,9.286-46.211,26.155-63.077l97.421-97.432l97.486,97.48 C230.11,302.113,222.487,327.697,222.487,355.033z M367.244,477.516c-67.539,0-122.486-54.947-122.486-122.486 c0-29.769,10.685-57.082,28.407-78.332l172.411,172.41C424.326,466.831,397.013,477.516,367.244,477.516z M461.322,433.362 l-172.41-172.411c21.249-17.722,48.564-28.407,78.332-28.407c67.539,0,122.486,54.947,122.486,122.486 C489.73,384.799,479.046,412.112,461.322,433.362z"
  })))));
}
const ForwardRef = /*#__PURE__*/React.forwardRef(SvgDrugs);
export default __webpack_public_path__ + "static/media/drugs.7897a3b6dfcd25343aba32be18b44a03.svg";
export { ForwardRef as ReactComponent };