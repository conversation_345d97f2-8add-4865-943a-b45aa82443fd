import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import Sidebar from "@components/admin/Sidebar";
import Trash from "@images/svg/trash-white.svg";
import User from '@images/svg/user.svg'
import { Link } from "react-router-dom";
import { useNavigate } from "react-router-dom";
import { getLoggedUser } from "@redux/actions/authAction";
import { getAllUsers, deleteUserAdmin } from "@redux/actions/usersAction";
import ShopNavbar from '@components/shop/ShopNavbar';
import ShopFooter from "@components/shop/ShopFooter";



const ManageUsers = () => {
  const [usersList, setUsersList] = useState([]);
  const [allUsers, setAllUsers] = useState([]);

  const dispatch = useDispatch();
  const usersData = useSelector((state) => state.usersReducer.allUsers);
  const loggedUserData = useSelector((state) => state.authReducer.getMe);

  const navigate = useNavigate();

  //delete coupon
  const handelDeleteUser = (id) => {
    dispatch(deleteUserAdmin(id));
    window.location.reload();
  };

  useEffect(() => {
    dispatch(getLoggedUser());
    dispatch(getAllUsers());
  }, []);

  useEffect(() => {
    if (usersData) {
      if (usersData.status === 200) {
        if (usersData.data) {
          setUsersList(usersData.data.data);
          setAllUsers(usersData.data.data);
        }
      }
    }
  }, [usersData]);

  useEffect(()=>{
    if(loggedUserData){
      if(loggedUserData.status === 200){
        if(loggedUserData.data){
          if(loggedUserData.data.data.role !== "admin"){
            navigate('/shop')
          }
        }
      }else if(loggedUserData.status === 401){
        navigate('/shop')
      }
    }
  },[loggedUserData])

  const onSearchUser = (e)=>{
    if(e.target.value !== ""){
      const filtredUserList = usersList.filter((user)=> user.email.includes(e.target.value))
      setUsersList(filtredUserList)
    }else{
      setUsersList(allUsers)
    }
  }

  return (
    <div className="admin-page">
      <ShopNavbar/>
      <div className="container">
        <div className="admin-page-wraper">
          <Sidebar />
          <section className="dashboard-container">
            <h3>Manage Users</h3>
            <div className="search-form">
              <input type="text" placeholder="Enter Email To Search" onChange={(e)=>onSearchUser(e)}/>
            </div>
            <div className="add-wraper">
              {usersList.length > 0 ? (
                usersList.map((user, index) => (
                  <div className="coupon-card" key={index}  style={{backgroundImage: user.role === "admin" ? `radial-gradient( circle farthest-corner at 17.1% 22.8%,  rgb(24, 226, 58) 0%, rgb(6, 160, 32) 90% )`:""}}>
                    <img
                      className="trash"
                      src={Trash}
                      alt="delete-coupon"
                      onClick={() => handelDeleteUser(user._id)}
                    />
                    <Link className="user-page" to={`/admin/user-details/${user._id}`}>
                    <div className="coupon-img">
                    <img src={User} alt="discount"/>
                    </div>
                    </Link>
                    <ul className="coupon-details">
                      <li>
                        Username:
                        <p>{user.name}</p>
                      </li>
                      <li>
                        Email:
                        <p>{user.email}</p>
                      </li>
                      <li>
                        Phone Number
                        <p>{user.phone}</p>
                      </li>
                      <li>
                      Role:
                        <p>{user.role}</p>
                      </li>
                    </ul>
                  </div>
                ))
              ) : (
                <p className="no-items">
                  لا يوجد مستخدمين حتى الأن
                  <Link to="/admin/add-coupon">إضافة مٌستخدم</Link>
                </p>
              )}
            </div>
          </section>
        </div>
      </div>
      <ShopFooter/>
    </div>
  );
};

export default ManageUsers