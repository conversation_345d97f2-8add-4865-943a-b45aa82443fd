import React from 'react';
import ReactDOM from 'react-dom/client';
import App from '../App';
import { Provider } from 'react-redux';
import store from '../redux/store';

import { json } from '@remix-run/node';

export const loader = async () => {
  return json({});
};

if (typeof document !== 'undefined') {
    const rootElement = document.getElementById('root');
    if (rootElement) {
      const root = ReactDOM.createRoot(document.getElementById('root'));
      root.render(
        <Provider store={store}>
          <App />
        </Provider>
      );
    }
  };