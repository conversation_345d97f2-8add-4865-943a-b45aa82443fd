var _g;
function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
import * as React from "react";
function SvgProducts(_ref, svgRef) {
  let {
    title,
    titleId,
    ...props
  } = _ref;
  return /*#__PURE__*/React.createElement("svg", _extends({
    fill: "#FFF",
    height: "800px",
    width: "800px",
    id: "Layer_1",
    xmlns: "http://www.w3.org/2000/svg",
    xmlnsXlink: "http://www.w3.org/1999/xlink",
    viewBox: "0 0 512 512",
    xmlSpace: "preserve",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _g || (_g = /*#__PURE__*/React.createElement("g", null, /*#__PURE__*/React.createElement("g", null, /*#__PURE__*/React.createElement("path", {
    d: "M107.888,84.09l16.235-16.235h298.432c8.504,0,15.422-6.918,15.422-15.422V15.422C437.976,6.918,431.058,0,422.554,0 H89.446c-8.504,0-15.422,6.918-15.422,15.422v37.012c0,8.504,6.918,15.422,15.422,15.422h8.505l-3.15,3.15 c-13.398,13.398-20.777,31.214-20.777,50.161v97.822v123.373v24.675c0,5.111,4.143,9.253,9.253,9.253 c5.11,0,9.253-4.142,9.253-9.253v-15.422h72.298c19.167,37.049,52.87,61.687,91.172,61.687c38.301,0,72.005-24.638,91.172-61.687 h72.298v101.783c0,22.11-17.988,40.096-40.096,40.096H132.627c-22.109,0-40.096-17.987-40.096-40.096v-49.344 c0-5.111-4.143-9.253-9.253-9.253c-5.11,0-9.253,4.142-9.253,9.253v49.344c0,32.313,26.29,58.602,58.602,58.602h246.747 c32.313,0,58.602-26.29,58.602-58.602V342.361V218.988v-97.822c0-10.828-2.533-21.685-7.326-31.395 c-2.261-4.583-7.812-6.464-12.393-4.202c-4.582,2.261-6.464,7.811-4.202,12.393c3.542,7.177,5.415,15.201,5.415,23.204v88.569 h-72.298c-19.167-37.049-52.87-61.687-91.172-61.687c-38.301,0-72.004,24.638-91.172,61.687H92.53v-88.569 C92.53,107.161,97.984,93.995,107.888,84.09z M92.53,49.349V18.506h326.94v30.843H92.53z M355.149,228.241h64.321v104.867h-64.321 c5.659-16.092,8.803-33.819,8.803-52.434C363.952,262.06,360.808,244.333,355.149,228.241z M256,166.554 c49.321,0,89.446,51.194,89.446,114.12S305.321,394.795,256,394.795s-89.446-51.194-89.446-114.12S206.679,166.554,256,166.554z  M156.851,228.241c-5.659,16.092-8.803,33.819-8.803,52.434c0,18.615,3.144,36.342,8.803,52.434H92.53V228.241H156.851z"
  })))));
}
const ForwardRef = /*#__PURE__*/React.forwardRef(SvgProducts);
export default __webpack_public_path__ + "static/media/products.04ea42982653fb5e0e1cd1d712730f34.svg";
export { ForwardRef as ReactComponent };