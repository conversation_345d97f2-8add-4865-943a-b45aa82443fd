var _g;
function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
import * as React from "react";
function SvgTwitterBlog(_ref, svgRef) {
  let {
    title,
    titleId,
    ...props
  } = _ref;
  return /*#__PURE__*/React.createElement("svg", _extends({
    height: 512,
    viewBox: "0 0 152 152",
    width: 512,
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _g || (_g = /*#__PURE__*/React.createElement("g", {
    id: "Layer_2",
    "data-name": "Layer 2"
  }, /*#__PURE__*/React.createElement("g", {
    id: "Color_Icon",
    "data-name": "Color Icon"
  }, /*#__PURE__*/React.createElement("g", {
    id: "_04.Twitter",
    "data-name": "04.Twitter"
  }, /*#__PURE__*/React.createElement("rect", {
    id: "Background",
    fill: "#03a9f4",
    height: 152,
    rx: 12,
    width: 152
  }), /*#__PURE__*/React.createElement("path", {
    id: "Icon",
    d: "m125.23 45.47a42 42 0 0 1 -11.63 3.19 20.06 20.06 0 0 0 8.88-11.16 40.32 40.32 0 0 1 -12.8 4.89 20.18 20.18 0 0 0 -34.92 13.8 20.87 20.87 0 0 0 .47 4.6 57.16 57.16 0 0 1 -41.61-21.11 20.2 20.2 0 0 0 6.21 27 19.92 19.92 0 0 1 -9.12-2.49v.22a20.28 20.28 0 0 0 16.17 19.82 20.13 20.13 0 0 1 -5.29.66 18 18 0 0 1 -3.83-.34 20.39 20.39 0 0 0 18.87 14.06 40.59 40.59 0 0 1 -25 8.61 36.45 36.45 0 0 1 -4.83-.28 56.79 56.79 0 0 0 31 9.06c37.15 0 57.46-30.77 57.46-57.44 0-.89 0-1.75-.07-2.61a40.16 40.16 0 0 0 10.04-10.48z",
    fill: "#fff"
  }))))));
}
const ForwardRef = /*#__PURE__*/React.forwardRef(SvgTwitterBlog);
export default __webpack_public_path__ + "static/media/twitter-blog.af11e2aec47c948b4172d8b6d33fbe64.svg";
export { ForwardRef as ReactComponent };