@use './Variables' as *;


.info {
  position: relative;
  z-index: 0; // تأكد أنها أسفل الانترو
  background-image: url("@images/imgs/bg-body1.webp");
  border-bottom: 1px solid #444;
  border-top: 1px solid #444;
  padding: 130px 0 100px;
  margin-top: 0; // القيمة الافتراضية

  // عندما يكون الانترو فوقه (السلايد الأول)، اسمح ببروز الصورة السفلي
  .intro.is-overlap + &,
  section.intro.is-overlap + & {
    position: relative;
    z-index: 1; // ثابت تحت الانترو بز-index أعلى منه في الانترو
    margin-top: -80px !important; // بروز أكبر للصورة فوق info
    padding-top: calc(130px + 80px);
  }
  position: relative;
  width: 100%;
  .container {
    .info-wraper {
      align-items: center;
      display: flex;
      width: 100%;
      .details-container {
        height: auto;
        width: 75%;
        .details {
          width: 100%;
          h3 {
            align-items: center;
            color: #fff;
            cursor: pointer;
            display: none;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 35px;
            small {
              color: #fbfbef;
              font-size: 14px;
              font-weight: 300;
              margin-right: 15px;
            }
            &.active {
              color: $mainColor;
              small {
                color: #fff;
              }
            }
          }
          .inner-details {
            display: flex;
            .img {
              border: 1px solid #444;
              flex-shrink: 0;
              height: 355px;
              margin-right: 50px;
              margin-top: 20px;
              width: 260px;
              img {
                height: 100%;
                object-fit: cover;
                width: 100%;
              }
            }
            .inner-info {
              padding-top: 20px;
              h5 {
                color: $mainColor;
                font-size: 14px;
                font-weight: 600;
                letter-spacing: 1px;
                margin-bottom: 5px;
                padding-left: 60px;
                position: relative;
                &::before {
                  background-color: $mainColor;
                  content: "";
                  height: 1px;
                  left: 0;
                  position: absolute;
                  top: 50%;
                  width: 30px;
                }
              }
              h2 {
                align-items: center;
                color: #fff;
                display: flex;
                font-size: 40px;
                font-weight: 600;
                margin-bottom: 5px;
                span {
                  font-size: 1.5em;
                  font-weight: bold;
                  margin-right: 20px;
                  opacity: 0.1;
                }
              }
              p {
                color: #fbfbef;
                font-size: 16px;
                font-weight: 300;
                line-height: 1.7;
                margin-bottom: 10px;
                span {
                  color: $mainColor;
                  font-weight: 500;
                }
                &:last-of-type {
                  margin-bottom: 0;
                }
              }
              .read-more {
                background-color: transparent;
                border: none;
                border-left: 1px solid $mainColor;
                border-right: 1px solid $mainColor;
                color: #fff;
                display: block;
                font-size: 12px;
                font-weight: 400;
                letter-spacing: 0.1em;
                margin-top: 30px;
                padding: 0 10px;
                width: 100px;
              }
            }
          }
          &.ar {
            direction: rtl;
            font-family: 'Noto Kufi Arabic', Arial, sans-serif !important;
            .inner-details {
              .img {
                margin-left: 50px;
              }
              .inner-info {
                h5 {
                  padding-right: 40px;
                  &::before {
                    right: 0;
                  }
                }
                h2 {
                  margin-bottom: 5px;
                  span {
                    font-size: 1.5em;
                    font-weight: bold;
                    margin-left: 20px;
                    opacity: 0.1;
                  }
                }
                p {
                  color: #fbfbef;
                  font-size: 16px;
                  font-weight: 300;
                  line-height: 1.7;
                  margin-bottom: 10px;
                  span {
                    color: $mainColor;
                    font-weight: 500;
                    margin-left: 5px;
                  }
                  &:last-of-type {
                    margin-bottom: 0;
                  }
                }
                .read-more {
                  background-color: transparent;
                  border: none;
                  border-left: 1px solid $mainColor;
                  border-right: 1px solid $mainColor;
                  color: #fff;
                  display: block;
                  font-size: 12px;
                  font-weight: 400;
                  letter-spacing: 0.1em;
                  margin-right: auto;
                  margin-top: 30px;
                  padding: 0 10px;
                  width: 100px;
                }
              }
            }
          }
        }
      }
      .links {
        padding-left: 50px;
        width: 25%;
        ul {
          height: 100%;
          width: 100%;
          li {
            align-items: center;
            color: #fff;
            cursor: pointer;
            display: flex;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 35px;
            &:hover,
            &.active {
              color: $mainColor;
              small {
                color: #fff;
              }
            }
            small {
              color: #fbfbef;
              font-size: 14px;
              font-weight: 300;
              margin-right: 15px;
            }
          }
          &.ar {
            direction: rtl;
            font-family: 'Noto Kufi Arabic', Arial, sans-serif !important;
            padding-right: 50px;
            li {
              small {
                margin-left: 15px;
              }
            }
          }
        }
        &.ar {
          padding-left: unset;
        }
      }
    }
  }
}
// استجابة للشاشات الصغيرة
@media screen and (width <= 576px) {
  .info {
    .container {
      .info-wraper {
        padding-left: 10px;
        .details-container {
          height: fit-content;
          width: 100%;
          .details {
            margin-bottom: 30px;
            h3 {
              display: flex;
              margin-bottom: 10px;
            }
            .inner-details {
              flex-direction: column;
              .img {
                height: 300px;
                margin-right: 0;
                width: 85%;
              }
              .inner-info {
                h2 {
                  font-size: 36px;
                }
                .read-more {
                  font-size: 14px;
                  text-align: center;
                  width: 120px;
                }
              }
            }
            &.ar {
              h3 {
                small {
                  margin-left: 15px;
                  margin-right: unset;
                }
              }
            }
          }
        }
        .links {
          display: none;
        }
      }
    }
  }
}


// md screen
@media screen and (max-width: 577px) {
    .info {
        .container {
            .info-wraper {
                padding-left: 10px;
                .details-container {
                    height: fit-content;
                    width: 100%;
                    .details {
                        margin-bottom: 30px;
                        h3 {
                            display: flex;
                            margin-bottom: 10px;
                        }
                        .inner-details {
                            .img {
                                width: 85%;
                                height: 300px;
                                margin-right: 0;
                            }
                            .inner-info {
                                .read-more{
                                    width: 120px;
                                    text-align: center;
                                    font-size: 14px;
                                }
                            }
                        }
                    }
                }
                .links {
                    display: none;
                }
            }
        }
    }
}

// ========== تنسيقات قوية لضمان عدم تغطية الصورة ==========
// إجبار info أن يكون تحت الانترو
.info {
  z-index: 0 !important;
  position: relative !important;
}

// إجبار البروز عندما يكون الانترو فوقه
.intro.is-overlap + .info,
section.intro.is-overlap + .info {
  margin-top: -120px !important;
  padding-top: calc(130px + 120px) !important;
  z-index: 1 !important;
  position: relative !important;
}

// منع أي تغطية للصورة
.info .overlay {
  z-index: 0 !important;
}

.info .container {
  z-index: 1 !important;
  position: relative !important;
}
