var _path;
function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
import * as React from "react";
function SvgNature(_ref, svgRef) {
  let {
    title,
    titleId,
    ...props
  } = _ref;
  return /*#__PURE__*/React.createElement("svg", _extends({
    width: "800px",
    height: "800px",
    viewBox: "0 0 16 16",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    fill: "#8f6B29",
    fillRule: "evenodd",
    d: "M13.3256006,7.32558062 C12.0516006,8.59953062 10.3124006,8.94299062 9,8.99311062 L9,14 C9,14.5523006 8.55228062,15 8,15 C7.44772062,15 7,14.5523006 7,14 L7,10.9931006 C5.68757062,10.9430006 3.94837062,10.5995006 2.67442062,9.32558062 C0.348837624,7 1.12403062,3.12403062 1.12403062,3.12403062 C1.12403062,3.12403062 5,2.34884062 7.32558062,4.67442062 C7.35231062,4.70114062 7.37862062,4.72808062 7.40454062,4.75520062 C7.65733062,4.02001062 8.05637062,3.29246062 8.67442062,2.67442062 C11,0.348837624 14.8760006,1.12403062 14.8760006,1.12403062 C14.8760006,1.12403062 15.6512006,5 13.3256006,7.32558062 Z M9.00920062,6.99080062 C9.07159062,6.98768062 9.13502062,6.98371062 9.19932062,6.97876062 C10.2275006,6.89967062 11.2265006,6.59628062 11.9114006,5.91137062 C12.5963006,5.22645062 12.8997006,4.22752062 12.9788006,3.19932062 C12.9837006,3.13502062 12.9877006,3.07159062 12.9908006,3.00920062 C12.9284006,3.01232062 12.8650006,3.01629062 12.8007006,3.02124062 C11.7725006,3.10033062 10.7735006,3.40372062 10.0886006,4.08863062 C9.40372062,4.77355062 9.10033062,5.77248062 9.02124062,6.80068062 C9.01629062,6.86498062 9.01232062,6.92841062 9.00920062,6.99080062 Z M6.80068062,8.97876062 C6.86498062,8.98371062 6.92841062,8.98768062 6.99080062,8.99080062 C6.98768062,8.92841062 6.98371062,8.86498062 6.97876062,8.80068062 C6.89967062,7.77248062 6.59628062,6.77355062 5.91137062,6.08863062 C5.22645062,5.40372062 4.22752062,5.10033062 3.19932062,5.02124062 C3.13502062,5.01629062 3.07159062,5.01232062 3.00920062,5.00920062 C3.01232062,5.07159062 3.01629062,5.13502062 3.02124062,5.19932062 C3.10033062,6.22752062 3.40372062,7.22645062 4.08863062,7.91137062 C4.77355062,8.59628062 5.77248062,8.89967062 6.80068062,8.97876062 Z"
  })));
}
const ForwardRef = /*#__PURE__*/React.forwardRef(SvgNature);
export default __webpack_public_path__ + "static/media/nature.70b79854c475236865251404a59583b3.svg";
export { ForwardRef as ReactComponent };