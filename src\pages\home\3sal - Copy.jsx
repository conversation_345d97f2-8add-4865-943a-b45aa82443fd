import React from 'react';
import { Link } from 'react-router-dom';
import Navbar from "@utilities/Navbar";
import Footer from "@utilities/Footer";
import M1 from "@images/molasses/1.webp";
import M2 from "@images/molasses/2.webp";
import M3 from "@images/molasses/3.webp";
import M4 from "@images/molasses/4.webp";
import M5 from "@images/molasses/5.webp";
import M6 from "@images/molasses/6.webp";
import M7 from "@images/molasses/7.webp";
import M8 from "@images/molasses/8.webp";
import M9 from "@images/molasses/9.webp";
import M10 from "@images/molasses/10.webp";
import M11 from "@images/molasses/11.webp";
import M12 from "@images/molasses/12.webp";
import M13 from "@images/molasses/13.webp";
import M14 from "@images/molasses/14.webp";
import M15 from "@images/molasses/15.webp";
import M16 from "@images/molasses/16.webp";
import M17 from "@images/molasses/17.webp";
import M18 from "@images/molasses/18.webp";
import M19 from "@images/molasses/19.webp";
import M20 from "@images/molasses/20.webp";
import M21 from "@images/molasses/21.webp";
import M22 from "@images/molasses/End.webp";

const 3sal = () => {
  return (
    <>
      <Navbar />
      <div className="about-page">
          <meta charSet="utf-8" />
          <title>Adam's Fancy Molasses</title>
        <div className="about-container">
          <div className="about-intro">
            <div className="container">
              <div className="intro-info">
                <h3>Egyptian Fancy Cane Molasses
                  <br/>
                  conentrated sugarcane syrup
                </h3>
                <p>
                  Are you ready to discover the secret behind
                  <br />
                  Pharaohs's strength?
                </p>
              </div>
            </div>
          </div>
        </div>

export default function 3sal({
  HeroImg = M1,
  AdamMolassesImg = M9,
  FamilyImg = M7,
  DrinksImg = M5,
  KidsImg = M6,
  VariantsImg = M8,
}) {
  const [openNutrition, setOpenNutrition] = useState(false);

  // Nutrition data (example -- عدّل القيم هنا بحسب شهادات التحليل الفعلية)
  const nutritionData = {
    "Energy": "210.9 Kcal",
    "Total Fat": "0 g",
    "Protein": "1.1 g",
    "Carbohydrates": "54.5 g",
    "Total Sugars": "50 g",
    "Iron (Fe)": "10.7 mg",
    "Calcium (Ca)": "60 – 205 mg",
    "Magnesium (Mg)": "242 mg",
    "Potassium (K)": "1460 mg",
    "Chromium (Cr)": "0.026 mg",
    "Phosphorus (P)": "26 – 31 mg",
    "Copper (Cu)": "0.49 mg",
    "Manganese (Mn)": "1.53 mg",
    "Sodium (Na)": "25.4 mg",
    "Selenium (Se)": "17.8 µg",
    "Zinc (Zn)": "0.29 mg",
    "Total polyphenols (GAE)": "497 mg",
    "Vitamin B1 (Thiamin)": "0.041 mg",
    "Vitamin B2 (Riboflavin)": "0.002 mg",
    "Niacin (B3)": "0.93 mg",
    "Vitamin B5 (Pantothenic acid)": "0.804 mg",
    "Vitamin B6": "0.67 mg",
    "Choline": "13.3 mg"
  };

  return (
    <div dir="rtl" className="min-h-screen bg-gradient-to-b from-gray-900 via-gray-800 to-gray-950 text-gray-100 font-sans">
      {/* NAVBAR */}
      <header className="sticky top-0 z-50 backdrop-blur bg-black/40 border-b border-gray-800">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            <div className="flex items-center gap-4">
              <a href="/" className="text-xl font-extrabold text-amber-300">Adam</a>
              <nav className="flex items-center gap-3">
                <a href="/adam/molasses" className="text-sm text-amber-100 hover:text-white">العسل الأسود — Adam</a>
                <span className="mx-1 text-gray-600">|</span>
                <a href="/mentor/superfoods" className="text-sm text-gray-300 hover:text-white">عالم السوبر فودز — Mentor</a>
              </nav>
            </div>

            <div className="flex items-center gap-4">
              <a href="/shop" className="text-sm px-4 py-2 rounded-md bg-amber-500 text-black font-semibold hover:bg-amber-400">تسوق الآن</a>
              <a href="/contact" className="text-sm text-gray-300 hover:text-white">تواصل معنا</a>
            </div>
          </div>
        </div>
      </header>

      {/* HERO */}
      <section className="max-w-7xl mx-auto px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
          <div className="space-y-6">
            <h1 className="text-4xl lg:text-5xl font-extrabold text-amber-300">Adam — إرث النكهة والصحة</h1>
            <p className="text-lg text-gray-300 max-w-2xl">
              نفخر بتقديم <span className="font-semibold text-white">العسل الأسود</span> في صورته الأكثر نقاءً وفخامة. منتجنا الأول — نقطة الانطلاق لعالم Adam — الذي يجمع بين التراث الغذائي لعدة حضارات وجودة تصنيع حديثة تلائم ذوق الأسرة الراقية.
            </p>

            <div className="flex gap-4">
              <a href="#why" className="inline-flex items-center gap-2 px-6 py-3 rounded-lg bg-amber-400 text-black font-semibold shadow-lg hover:scale-[1.02] transform transition">لماذا Adam؟</a>
              <a href="#family" className="inline-flex items-center gap-2 px-6 py-3 rounded-lg border border-amber-500 text-amber-200 hover:bg-black/30">للعائلة</a>
            </div>

            <p className="text-sm text-gray-400">
              ملاحظة: جميع المعلومات بصيغة تغذوية/تثقيفية — للمواضيع الطبية استشر أخصائي.
            </p>
          </div>

          <div className="relative">
            <div className="rounded-2xl overflow-hidden shadow-2xl ring-1 ring-black/20">
              <img src={HeroImg} alt="Adam Molasses Hero" className="w-full h-96 object-cover" />
            </div>

            <div className="absolute -left-6 -bottom-6 transform rotate-3">
              <div className="bg-black/70 backdrop-blur text-amber-50 rounded-xl p-4 shadow-lg w-72 ring-1 ring-amber-200/10">
                <div className="text-xs opacity-80">المنتج الأول في التشكيلة</div>
                <div className="font-semibold text-lg mt-1">العسل الأسود — Adam</div>
                <div className="text-sm text-gray-300 mt-2">طعم أصيل، جودة فاخرة — مُعدّ للاستخدام اليومي في مطبخ الأسرة الراقية.</div>
                <a href="#catalog" className="mt-3 inline-block text-sm text-amber-300 hover:underline">اكتشف الكتالوج</a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* WHY / PHILOSOPHY */}
      <section id="why" className="max-w-6xl mx-auto px-6 lg:px-8 py-12">
        <div className="bg-gradient-to-br from-gray-850 to-gray-800 rounded-3xl p-8 ring-1 ring-black/30 shadow-xl">
          <h2 className="text-2xl font-bold text-amber-200">رؤيتنا مع Adam</h2>
          <p className="mt-4 text-gray-300 leading-relaxed">
            Adam ليست مجرد علامة — إنها رؤية لصياغة تراث الغذاء الطبيعي في قالب معاصر فاخر. نؤمن أن المنتج الفاخر يجب أن يقدم تجربة كاملة: طعمٌ متميز، تعبئة راقية، ومواصفات تغذوية واضحة وموثوقة. بدءاً من هذا العسل الأسود، سنبني تشكيلة موسّعة تشمل مشروبات طاقة طبيعية للشباب، حلويات أطفال صحية، وأنواع متعددة من الدبس (العنب، البنجر، الرمان...) لتثري المطبخ العصري بكل الأذواق.
          </p>
        </div>
      </section>

      {/* CATALOG / HERO PRODUCT */}
      <section id="catalog" className="max-w-7xl mx-auto px-6 lg:px-8 py-12">
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Product Card */}
          <article className="bg-gradient-to-br from-black/60 to-gray-900 rounded-3xl p-6 shadow-2xl ring-1 ring-amber-200/10">
            <div className="flex items-start gap-4">
              <div className="w-28 h-28 rounded-xl overflow-hidden ring-1 ring-black/20">
                <img src={AdamMolassesImg} alt="Adam Molasses" className="object-cover w-full h-full" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-amber-200">العسل الأسود — Adam</h3>
                <p className="text-sm text-gray-300 mt-1">النسخة الفاخرة من دبس القصب، مُحضّرة للحياة العائلية الراقية.</p>
                <div className="mt-3 text-sm text-gray-400">
                  طاقة طبيعية للطبخ والتحلية، مع تركيز واضح للمعادن ومركبات نباتية قيّمة.
                </div>
              </div>
            </div>

            <div className="mt-5 border-t border-gray-800 pt-4">
              <h4 className="text-sm font-semibold text-amber-100">لمحة تغذوية آمنة</h4>
              <ul className="text-sm text-gray-300 mt-2 list-disc list-inside space-y-1">
                <li>مصدر طبيعي للحديد والمعادن.</li>
                <li>قيمة طاقة مناسبة للاستخدام في الطهي والتحلية.</li>
                <li>منتج مُنتقًى ومن دون إضافات صناعية عند المصدر.</li>
              </ul>

              <div className="mt-4">
                <button
                  onClick={() => setOpenNutrition(!openNutrition)}
                  className="px-4 py-2 rounded-md bg-amber-400 text-black font-medium hover:bg-amber-300"
                >
                  {openNutrition ? "إخفاء حقائق التغذية" : "عرض جدول الحقائق الغذائية"}
                </button>

                {openNutrition && (
                  <div className="mt-4 overflow-x-auto bg-black/60 p-4 rounded-lg ring-1 ring-black/30">
                    <table className="w-full text-sm text-gray-100">
                      <thead>
                        <tr className="text-left border-b border-gray-700">
                          <th className="py-2">المكوّن</th>
                          <th className="py-2">لكل 100 جم</th>
                        </tr>
                      </thead>
                      <tbody>
                        {Object.entries(nutritionData).map(([k, v]) => (
                          <tr key={k} className="border-b border-gray-800 odd:bg-black/30">
                            <td className="py-2">{k}</td>
                            <td className="py-2 font-medium text-amber-100">{v}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                    <div className="text-xs text-gray-400 mt-3">
                      * القيم تعتمد على تحاليل مختبرية؛ يُرجى الرجوع لشهادة التحليل المرفقة للنسخ الرسمية.
                    </div>
                  </div>
                )}
              </div>

              <div className="mt-5 flex gap-3">
                <a href="/adam/molasses" className="px-4 py-2 rounded-md bg-amber-300 text-black font-semibold hover:bg-amber-200">عرض المنتج</a>
                <a href="#family" className="px-4 py-2 rounded-md border border-amber-400 text-amber-200 hover:bg-black/20">كيف تستخدمه للعائلة؟</a>
              </div>
            </div>
          </article>

          {/* Family & Uses */}
          <article className="bg-black/60 rounded-3xl p-6 shadow-xl ring-1 ring-amber-200/10">
            <h3 className="text-xl font-bold text-amber-200">لمن؟ — منتج لكل أفراد العائلة</h3>
            <p className="text-gray-300 mt-2">
              العسل الأسود من Adam مُصمم ليكون جزءًا عمليًا من مائدة الأسرة الراقية: سهل الاستخدام، متعددِ الوظائف، ويجذب الأذواق بفضل نكهته العميقة.
            </p>

            <div className="mt-4 space-y-3">
              <div className="p-3 rounded-lg bg-black/40 border border-gray-800">
                <div className="flex items-start gap-3">
                  <div className="text-amber-300 font-semibold">الأطفال</div>
                  <div className="text-sm text-gray-300">وصفات حلويات طبيعية ومحليات صحية منخفضة مع مزج مناسب ضمن النظام الغذائي</div>
                </div>
              </div>

              <div className="p-3 rounded-lg bg-black/40 border border-gray-800">
                <div className="flex items-start gap-3">
                  <div className="text-amber-300 font-semibold">الشباب</div>
                  <div className="text-sm text-gray-300">شراب طاقة طبيعي للتحضير المنزلي ووصفات سريعة للطاقة في الأيام النشيطة</div>
                </div>
              </div>

              <div className="p-3 rounded-lg bg-black/40 border border-gray-800">
                <div className="flex items-start gap-3">
                  <div className="text-amber-300 font-semibold">البالغون</div>
                  <div className="text-sm text-gray-300">بديل تحلية غني بالمعادن يمكن إدراجه في وصفات الطهي والخبز الفاخرة</div>
                </div>
              </div>
            </div>

            <div className="mt-4 text-sm text-gray-400">
              نعمل أيضاً على تطوير خطوط إنتاج مستقبلية مخصصة مثل: مشروبات طاقة طبيعية، حلويات أطفال صحية، وأنواع دبس متعددة (عنب، رمان، بنجر) لتلبية ذائقتكم واحتياجات السوق الفاخر.
            </div>
          </article>

          {/* Future Variants / Story */}
          <article className="bg-gradient-to-br from-black/50 to-gray-900 rounded-3xl p-6 shadow-2xl ring-1 ring-amber-200/10">
            <h3 className="text-xl font-bold text-amber-200">تراث عالمي — حضور محلي</h3>
            <p className="text-gray-300 mt-2">
              عبر التاريخ والعادات الغذائية المتنوعة، مثل هذا المنتج كان حجر ربط بين ثقافات كثيرة — من الشرق الأوسط إلى آسيا وأوروبا. نحن نعيد إحياء هذا الإرث بنهج تصنيع عصري ومعايير جودة عالية تناسب ذوق العائلة الراقية.
            </p>

            <div className="mt-4 grid gap-3">
              <div className="flex items-start gap-3">
                <img src={VariantsImg} alt="variants" className="w-16 h-12 object-cover rounded-md" />
                <div>
                  <div className="font-semibold text-amber-200">منتجات مستقبلية</div>
                  <div className="text-sm text-gray-300">دبس العنب — دبس الرمان — دبس البنجر — مركبات نكهة طبخ</div>
                </div>
              </div>

              <div className="pt-2">
                <a href="/adam/collections" className="text-sm px-4 py-2 rounded-md bg-amber-300 text-black font-medium hover:bg-amber-200">استكشف التشكيلة المستقبلية</a>
              </div>
            </div>
          </article>
        </div>
      </section>

      {/* RECIPES / IDEAS */}
      <section id="recipes" className="max-w-7xl mx-auto px-6 lg:px-8 py-12">
        <div className="bg-gray-900/60 rounded-3xl p-8 ring-1 ring-black/20">
          <h3 className="text-2xl font-bold text-amber-200">أفكار استخدام راقية</h3>
          <p className="text-gray-300 mt-2">بعض اقتراحات الطهاة لدمج العسل الأسود في وصفات تفخرون بتقديمها على مائدتكم.</p>

          <div className="mt-6 grid md:grid-cols-3 gap-6">
            <div className="p-4 rounded-lg bg-black/50 border border-gray-800">
              <h4 className="font-semibold text-amber-200">شراب طاقة منزلي</h4>
              <p className="text-sm text-gray-300 mt-2">امزج ملعقة عسل أسود مع ماء ليمون ونعناع — وصفة سريعة لطاقة طبيعية.</p>
            </div>

            <div className="p-4 rounded-lg bg-black/50 border border-gray-800">
              <h4 className="font-semibold text-amber-200">تغميسة فاخرة</h4>
              <p className="text-sm text-gray-300 mt-2">امزج العسل الأسود مع الطحينة والقليل من عصير الليمون وزيت الزيتون لتتبيلة راقية للسلطات.</p>
            </div>

            <div className="p-4 rounded-lg bg-black/50 border border-gray-800">
              <h4 className="font-semibold text-amber-200">حلوى أطفال بنكهة طبيعية</h4>
              <p className="text-sm text-gray-300 mt-2">استخدم العسل الأسود كقاعدة لسناكس منزلي مع الشوفان والمكسرات — تحلية بمكونات مألوفة.</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="max-w-7xl mx-auto px-6 lg:px-8 py-12">
        <div className="rounded-3xl p-8 bg-gradient-to-br from-amber-600 to-amber-400 text-black shadow-2xl">
          <div className="flex flex-col lg:flex-row items-center justify-between gap-6">
            <div>
              <h3 className="text-2xl font-bold">اكتشف عالماً من النكهات — احجز تجربتك</h3>
              <p className="text-sm mt-2">انضم للقائمة لتكون أول من يجرب المنتجات الجديدة والتشكيلات الخاصة بالعلامة.</p>
            </div>
            <div className="flex gap-3">
              <a href="/shop" className="px-5 py-3 bg-black text-amber-300 rounded-md font-semibold hover:opacity-90">تسوق الآن</a>
              <a href="/subscribe" className="px-5 py-3 border border-black rounded-md text-black bg-white/90 hover:opacity-90">انضم إلى القائمة</a>
            </div>
          </div>
        </div>
      </section>

      {/* FOOTER */}
      <footer className="max-w-7xl mx-auto px-6 lg:px-8 pb-12">
        <div className="pt-8 border-t border-gray-800 text-sm text-gray-400">
          © {new Date().getFullYear()} Adam. All rights reserved. Designed for premium households & modern kitchens.
        </div>
      </footer>
    </>
  );
}
