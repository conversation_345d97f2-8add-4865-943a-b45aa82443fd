var _style, _path;
function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
import * as React from "react";
function SvgHome(_ref, svgRef) {
  let {
    title,
    titleId,
    ...props
  } = _ref;
  return /*#__PURE__*/React.createElement("svg", _extends({
    id: "Uploaded to svgrepo.com",
    xmlns: "http://www.w3.org/2000/svg",
    xmlnsXlink: "http://www.w3.org/1999/xlink",
    width: "800px",
    height: "800px",
    viewBox: "0 0 32 32",
    xmlSpace: "preserve",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _style || (_style = /*#__PURE__*/React.createElement("style", {
    type: "text/css"
  }, "\r\n\t.puchipuchi_een{fill:#FFF;}\r\n")), _path || (_path = /*#__PURE__*/React.createElement("path", {
    className: "puchipuchi_een",
    d: "M29.832,12.222c-0.192,0.289-0.51,0.445-0.833,0.445c-0.19,0-0.383-0.055-0.554-0.168L16,4.202 L3.555,12.499c-0.462,0.305-1.081,0.182-1.387-0.277c-0.307-0.46-0.183-1.08,0.277-1.387l13-8.667c0.336-0.225,0.773-0.225,1.109,0 l13,8.667C30.015,11.142,30.139,11.762,29.832,12.222z M4,13v6v5v4c0,1.1,0.9,2,2,2h7v-9c0-0.55,0.45-1,1-1h4c0.55,0,1,0.45,1,1v9h7 c1.1,0,2-0.9,2-2v-4v-5v-6L16,5L4,13z"
  })));
}
const ForwardRef = /*#__PURE__*/React.forwardRef(SvgHome);
export default __webpack_public_path__ + "static/media/home.3bf0c00688b0447e5acb9314e3d744ad.svg";
export { ForwardRef as ReactComponent };