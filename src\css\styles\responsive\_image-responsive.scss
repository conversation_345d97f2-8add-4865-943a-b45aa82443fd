/**
 * ملف استجابة الصور للشاشات المختلفة
 *
 * هذا الملف يحتوي على تصحيحات للصور في الشاشات المختلفة
 * تم تحسين الكود وإزالة تعليمات !important غير الضرورية
 *
 * الأنماط المعرفة في هذا الملف:
 * - استجابة للشاشات الكبيرة (992px - 1199px)
 * - استجابة للشاشات المتوسطة (768px - 991px)
 * - استجابة للشاشات الصغيرة (576px - 767px)
 * - استجابة للشاشات الصغيرة جدًا (أقل من 576px)
 * - استجابة للشاشات الصغيرة جدًا (أقل من 480px)
 *
 * المتغيرات والدوال المستوردة من ملفات أخرى:
 * - من ملف abstracts/_variables.scss: $img-circular-sizes, $breakpoint-lg, $breakpoint-md, $breakpoint-sm
 * - من ملف abstracts/_mixins.scss: @mixin respond-to, @mixin img-circular
 */

// =============================
// تم تبسيط الاستدعاءات: الآن نعتمد فقط على ملف أدوات الملاءمة الموحد
@use './_responsive-helpers.scss' as *;
@use "sass:map";

// ======================================

@include media('md', 'portrait') {
  // تصحيح عرض المحتوى
  .container {
    max-width: 95vw;
    overflow-x: hidden;
    padding: 0;
    width: 95vw;
  }

  body {
    overflow-x: hidden;
    width: 100%;
  }

  // تنسيقات عامة للفقرات
  .paragraphs-wraper .paragraph {
    flex-direction: column;
    align-items: center;
    width: 100%;
  }

  .paragraphs-wraper .paragraph .paragraph-img {
    display: block;
    align-items: center;
    margin: 0 auto;
    justify-content: center;
    width: 100vw;
    order: 1;
    height: auto;
  }

  .paragraphs-wraper .paragraph .paragraph-content {
    order: 2;
    padding: 0;
    width: 100%;
  }
}