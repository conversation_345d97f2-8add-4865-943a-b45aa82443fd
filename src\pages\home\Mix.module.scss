// Mix.module.scss

// Theme colors
$bg: #fbfaf9;
$card: #ffffff;
$muted: #6b7280;
$accent: #b68b2e; // warm gold
$accent-dark: #8a6b24;
$ink: #0f1724;
$glass: rgba(255,255,255,0.65);

// Typography
$serif: "Playfair Display", serif;
$sans: "Inter", system-ui, -apple-system, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial;

@keyframes fadeUp {
  from {
    opacity: 0;
    transform: translateY(12px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page {
  font-family: $sans;
  background: linear-gradient(180deg, $bg 0%, #f3f3f3 100%);
  color: $ink;
  min-height: 100vh;
}

/* Header / Navbar */
.header {
  position: sticky;
  top: 0;
  z-index: 40;
  backdrop-filter: blur(6px);
  background: rgba(255,255,255,0.75);
  border-bottom: 1px solid rgba(15,23,36,0.06);
}

.navInner {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.navLeft {
  display: flex;
  gap: 1.25rem;
  align-items: center;
}

.navLink {
  color: $muted;
  text-decoration: none;
  font-weight: 600;
  &:hover { color: $accent-dark; }
}

.navActive {
  color: $accent-dark;
  font-weight: 700;
  text-decoration: none;
}

.shopBtn {
  background: $accent;
  color: #fff;
  padding: 0.5rem 1rem;
  border-radius: 999px;
  text-decoration: none;
  font-weight: 700;
  box-shadow: 0 8px 24px rgba(182,139,46,0.12);
  transition: transform .22s ease, box-shadow .22s ease;
  &:hover { transform: translateY(-3px); box-shadow: 0 12px 30px rgba(182,139,46,0.18); }
}

/* HERO */
.hero {
  max-width: 1200px;
  margin: 0 auto;
  padding: 4.5rem 1.5rem;
}

.heroGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2.5rem;
  align-items: center;
  animation: fadeUp .6s ease both;
}

.heroText {
  .h1 {
    font-family: $serif;
    font-size: 3.1rem;
    color: $accent;
    margin: 0 0 1rem 0;
    letter-spacing: -0.02em;
  }

  .lead {
    color: rgba(15,23,36,0.8);
    font-size: 1.05rem;
    line-height: 1.75;
    margin-bottom: 1.4rem;
    max-width: 52ch;
  }

  .cta {
    display: inline-block;
    background: $accent;
    color: #fff;
    padding: 0.75rem 1.25rem;
    border-radius: 999px;
    text-decoration: none;
    font-weight: 700;
    box-shadow: 0 10px 30px rgba(15,23,36,0.06);
    transition: transform .2s ease;
    &:hover { transform: translateY(-3px); }
  }
}

.heroImage img {
  width: 100%;
  height: 420px;
  object-fit: cover;
  border-radius: 16px;
  box-shadow: 0 20px 50px rgba(15,23,36,0.12);
}

/* CATALOG GRID */
.catalog {
  max-width: 1200px;
  margin: 2.5rem auto 4rem;
  padding: 0 1.5rem;
}

.grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
}

/* Product card */
.productCard {
  background: $card;
  border-radius: 14px;
  padding: 1.6rem;
  box-shadow: 0 12px 30px rgba(15,23,36,0.06);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  transition: transform .28s cubic-bezier(.2,.9,.2,1), box-shadow .28s ease;
  &:hover {
    transform: translateY(-10px);
    box-shadow: 0 30px 70px rgba(15,23,36,0.12);
  }
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sub {
  margin: 0;
  color: $muted;
  font-size: 0.9rem;
}

.thumb {
  width: 84px;
  height: 84px;
  object-fit: cover;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(15,23,36,0.06);
}

.cardBody {
  margin-top: 1rem;
  h4 { color: $accent; margin-bottom: 0.5rem; }
  ul { margin-left: 1rem; color: $muted; }
  h5 { margin-top: 1rem; font-weight: 700; }
  p { color: rgba(15,23,36,0.75); }
}

.btnPrimary {
  display: inline-block;
  margin-top: 1.2rem;
  padding: 0.6rem 1rem;
  background: $accent;
  color: #fff;
  border-radius: 999px;
  text-decoration: none;
  font-weight: 700;
  transition: transform .18s ease;
  &:hover { transform: translateY(-4px); }
}

.btnAccent {
  display: inline-block;
  margin-top: 1.2rem;
  padding: 0.6rem 1rem;
  background: #2a7a65;
  color: #fff;
  border-radius: 999px;
  text-decoration: none;
  font-weight: 700;
  &:hover { transform: translateY(-4px); }
}

.comboCard {
  background: linear-gradient(180deg, #ffffff 0%, #f7fbff 100%);
  border-radius: 14px;
  padding: 1.6rem;
  box-shadow: 0 14px 36px rgba(15,23,36,0.06);
}

.comboActions {
  display: flex;
  gap: 0.6rem;
  margin-top: 1rem;
}

.btnOutline {
  padding: 0.6rem 1rem;
  border-radius: 999px;
  border: 1px solid $accent;
  background: transparent;
  color: $accent-dark;
  text-decoration: none;
  font-weight: 700;
}

/* Usage section */
.usage {
  max-width: 1200px;
  margin: 0 auto 3.5rem;
  padding: 0 1.5rem;
}

.usageInner {
  background: $card;
  border-radius: 14px;
  padding: 2rem;
  box-shadow: 0 12px 30px rgba(15,23,36,0.04);
}

.usageGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-top: 1rem;
}

.usageCard {
  background: linear-gradient(180deg, #fff 0%, #fbfbfb 100%);
  border-radius: 12px;
  padding: 1rem;
  text-align: left;
  box-shadow: 0 6px 20px rgba(15,23,36,0.04);

  img { width: 100%; height: 160px; object-fit: cover; border-radius: 10px; margin-bottom: 0.5rem; }
  h4 { font-family: $serif; color: $accent-dark; }
  p { color: $muted; }
}

/* Nutrition table styles */
.nutritionWrap { margin-top: 1rem; }
.toggleBtn {
  background: transparent;
  color: $accent-dark;
  border: none;
  cursor: pointer;
  font-weight: 700;
}
.tableContainer {
  margin-top: .75rem;
  background: #fff;
  border-radius: 10px;
  padding: .75rem;
  box-shadow: 0 8px 20px rgba(15,23,36,0.04);
  overflow:auto;
}
.table {
  width: 100%;
  border-collapse: collapse;
  color: $ink;
  th, td { padding: .6rem .75rem; text-align: left; border-bottom: 1px solid rgba(15,23,36,0.06); }
  thead { background: #fafafa; }
}

/* Footer */
.footer {
  text-align: center;
  padding: 2.4rem 1rem;
  color: rgba(15,23,36,0.55);
  border-top: 1px solid rgba(15,23,36,0.04);
}

/* Responsive */
@media (max-width: 1024px) {
  .heroGrid { grid-template-columns: 1fr; }
  .grid { grid-template-columns: 1fr; }
  .usageGrid { grid-template-columns: 1fr; }
}
