var _path, _rect, _rect2, _rect3;
function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
import * as React from "react";
function SvgDateRange(_ref, svgRef) {
  let {
    title,
    titleId,
    ...props
  } = _ref;
  return /*#__PURE__*/React.createElement("svg", _extends({
    width: "800px",
    height: "800px",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    d: "M20 10V7C20 5.89543 19.1046 5 18 5H6C4.89543 5 4 5.89543 4 7V10M20 10V19C20 20.1046 19.1046 21 18 21H6C4.89543 21 4 20.1046 4 19V10M20 10H4M8 3V7M16 3V7",
    stroke: "#000000",
    strokeWidth: 2,
    strokeLinecap: "round"
  })), _rect || (_rect = /*#__PURE__*/React.createElement("rect", {
    x: 6,
    y: 12,
    width: 3,
    height: 3,
    rx: 0.5,
    fill: "#000000"
  })), _rect2 || (_rect2 = /*#__PURE__*/React.createElement("rect", {
    x: 10.5,
    y: 12,
    width: 3,
    height: 3,
    rx: 0.5,
    fill: "#000000"
  })), _rect3 || (_rect3 = /*#__PURE__*/React.createElement("rect", {
    x: 15,
    y: 12,
    width: 3,
    height: 3,
    rx: 0.5,
    fill: "#000000"
  })));
}
const ForwardRef = /*#__PURE__*/React.forwardRef(SvgDateRange);
export default __webpack_public_path__ + "static/media/date-range.3b5807bcf7ba7ee1097d286dd6cc417b.svg";
export { ForwardRef as ReactComponent };