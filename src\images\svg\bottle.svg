var _g;
function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
import * as React from "react";
function SvgBottle(_ref, svgRef) {
  let {
    title,
    titleId,
    ...props
  } = _ref;
  return /*#__PURE__*/React.createElement("svg", _extends({
    fill: "#8f6B29",
    id: "Capa_1",
    xmlns: "http://www.w3.org/2000/svg",
    xmlnsXlink: "http://www.w3.org/1999/xlink",
    width: "800px",
    height: "800px",
    viewBox: "0 0 320.864 320.864",
    xmlSpace: "preserve",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _g || (_g = /*#__PURE__*/React.createElement("g", null, /*#__PURE__*/React.createElement("g", {
    id: "Artwork_95_"
  }, /*#__PURE__*/React.createElement("g", {
    id: "Layer_5_95_"
  }, /*#__PURE__*/React.createElement("path", {
    d: "M184.04,81.831c-1.89-5.102-3.461-28.631-3.926-55.05c-0.003-0.102,0.107-0.372,0.514-0.372 c2.092-0.124,3.248-1.788,3.248-3.909V4c0-2.2-1.801-4-4-4h-38.885c-2.2,0-4,1.8-4,4v18.5c0,2.123,1.059,3.769,3.151,3.89 c0.673,0,0.613,0.478,0.609,0.673c-0.462,26.677-2.034,49.669-3.924,54.769c-11.119,29.994-35.884,27.392-35.884,63.394v128.735 c0,49.535,24.919,47.043,59.489,46.818c34.571,0.225,59.49,2.717,59.49-46.818V145.225 C219.924,109.223,195.159,111.826,184.04,81.831z M196.929,209.783c-5.844,8.889-31.004,30.021-36.457,30.021 c-5.338,0-30.727-21.211-36.532-30.021c-2.907-4.411-4.97-9.277-4.627-15.678c0.605-11.325,9.866-20.678,21.208-20.678 c11.659,0,18.449,12.439,19.914,12.439c1.675,0,8.755-12.439,19.914-12.439c11.342,0,20.602,9.353,21.207,20.678 C201.898,200.504,199.843,205.352,196.929,209.783z"
  }))))));
}
const ForwardRef = /*#__PURE__*/React.forwardRef(SvgBottle);
export default __webpack_public_path__ + "static/media/bottle.4981c54a41f9e252c5d5c446c7b4493b.svg";
export { ForwardRef as ReactComponent };