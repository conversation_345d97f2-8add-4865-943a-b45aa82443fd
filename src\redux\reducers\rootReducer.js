import { combineReducers } from '@reduxjs/toolkit';
import categoryReducer from './categoryReducer';
import authReducer from './authReducer';
import brandReducer from './brandReducer';
import wishlistReducer from './wishlistReducer';
import sizeReducer from './sizeReducer';
import productReducer from './productReducer';
import cartReducer from './cartReducer';
import couponReducer from './couponReducer';
import addressReducer from './addressReducer';
import orderReducer from './orderReducer';
import productReviewsReducer from './productReviewsReducer';
import usersReducer from './usersReducer';

const rootReducer = combineReducers({
    categoryReducer: categoryReducer,
    authReducer: authReducer,
    brandReducer: brandReducer,
    wishlistReducer: wishlistReducer,
    sizeReducer: sizeReducer,
    productReducer: productReducer,
    cartReducer: cartReducer,
    couponReducer: couponReducer,
    addressReducer: addressReducer,
    orderReducer: orderReducer,
    productReviewsReducer: productReviewsReducer,
    usersReducer: usersReducer,
});

export default rootReducer;