var _path, _path2, _path3, _path4, _path5;
function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
import * as React from "react";
function SvgCartProduct(_ref, svgRef) {
  let {
    title,
    titleId,
    ...props
  } = _ref;
  return /*#__PURE__*/React.createElement("svg", _extends({
    width: "800px",
    height: "800px",
    viewBox: "0 0 24 24",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    d: "M20.0164 16.2572C19.5294 18.5297 19.2859 19.666 18.4608 20.333C17.6357 21 16.4737 21 14.1495 21H9.85053C7.52639 21 6.36432 21 5.53925 20.333C4.71418 19.666 4.47069 18.5297 3.98372 16.2572L3.55514 14.2572C2.83668 10.9043 2.47745 9.22793 3.378 8.11397C4.27855 7 5.99302 7 9.42196 7H14.5781C18.0071 7 19.7215 7 20.6221 8.11397C21.2929 8.94376 21.2647 10.0856 20.9097 12",
    stroke: "#FFF",
    strokeWidth: 1.5,
    strokeLinecap: "round"
  })), _path2 || (_path2 = /*#__PURE__*/React.createElement("path", {
    d: "M16 12H12M9 12H8",
    stroke: "#FFF",
    strokeWidth: 1.5,
    strokeLinecap: "round",
    strokeLinejoin: "round"
  })), _path3 || (_path3 = /*#__PURE__*/React.createElement("path", {
    d: "M10 15H14",
    stroke: "#FFF",
    strokeWidth: 1.5,
    strokeLinecap: "round",
    strokeLinejoin: "round"
  })), _path4 || (_path4 = /*#__PURE__*/React.createElement("path", {
    d: "M18 9L15 3",
    stroke: "#FFF",
    strokeWidth: 1.5,
    strokeLinecap: "round",
    strokeLinejoin: "round"
  })), _path5 || (_path5 = /*#__PURE__*/React.createElement("path", {
    d: "M6 9L9 3",
    stroke: "#FFF",
    strokeWidth: 1.5,
    strokeLinecap: "round",
    strokeLinejoin: "round"
  })));
}
const ForwardRef = /*#__PURE__*/React.forwardRef(SvgCartProduct);
export default __webpack_public_path__ + "static/media/cart-product.1e53b3ff956cfb950ecab37b1a7df3a2.svg";
export { ForwardRef as ReactComponent };