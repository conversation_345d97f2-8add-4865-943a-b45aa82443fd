var _path;
function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
import * as React from "react";
function SvgMagnet(_ref, svgRef) {
  let {
    title,
    titleId,
    ...props
  } = _ref;
  return /*#__PURE__*/React.createElement("svg", _extends({
    fill: "#8f6B29",
    width: "800px",
    height: "800px",
    viewBox: "0 0 32 32",
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title === undefined ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, "magnet") : title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _path || (_path = /*#__PURE__*/React.createElement("path", {
    d: "M22.682 12.079c-1.994 4.614-5.628 8.464-10.441 10.559 6.032-0.677 10.413-5.414 10.441-10.559zM26.328 12.079c-2.691 6.225-7.593 11.42-14.088 14.246 8.138-0.914 14.049-7.305 14.088-14.246zM30.855 12.079c-3.556 8.225-10.033 15.090-18.614 18.824 10.753-1.208 18.563-9.652 18.614-18.824zM11.894 17.006c-1.637-1.292-3.494-2.787-4.081-3.374-2.383-2.383-2.658-4.74-0.838-6.559s4.148-1.537 6.559 0.875c0.581 0.581 2.051 2.393 3.334 4.007l2.233-2.268c-1.33-2.122-3.051-4.762-3.891-5.602-3.713-3.205-8.245-3.344-11.406-0.219h0c-3.599 3.559-2.995 8.265 0.182 11.443 0.852 0.852 3.555 2.61 5.692 3.948l2.215-2.25zM10.088 19.511c1.375 0.855 2.427 1.483 2.427 1.483l1.968-1.968c-0.384-0.296-1.238-0.957-2.216-1.727l-2.178 2.212zM17.162 12.325c0.786 0.993 1.465 1.864 1.765 2.254l1.968-1.968c0 0-0.655-1.097-1.538-2.516l-2.195 2.23z"
  })));
}
const ForwardRef = /*#__PURE__*/React.forwardRef(SvgMagnet);
export default __webpack_public_path__ + "static/media/magnet.1d18c91eabc579dda135d429afdce861.svg";
export { ForwardRef as ReactComponent };