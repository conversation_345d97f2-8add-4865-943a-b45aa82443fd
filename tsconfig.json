{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@components/*": ["src/components/*"],
      "@pages/*": ["src/pages/*"],
      "@styles/*": ["src/css/styles/*"],
      "@images/*": ["src/images/*"],
      "@utilities/*": ["src/utilities/*"],
      "@redux/*": ["src/redux/*"],
      "@api/*": ["src/api/*"],
    },
    "typeRoots": ["./node_modules/@types", "./src/types"],
    "incremental": true,
    "forceConsistentCasingInFileNames": true,
    "noImplicitAny": true,
    "noImplicitThis": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "strictBindCallApply": true,
    "strictPropertyInitialization": true,
    "noImplicitReturns": true,
    "noUncheckedIndexedAccess": true,
    "noImplicitOverride": true,
    "allowUnreachableCode": false,
    "allowUnusedLabels": false,
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }],
  "exclude": [
    "node_modules",
    "dist",
    "build",
    "coverage",
    "**/@babel/**",
    "**/istanbul-lib-report/**",
    "**/prop-types/**",
    "**/yargs-parser/**"
  ]
}
