@use './Variables' as *;

// قاعدة عامة لتطبيق تأثير الـhover والظل على كافة الصور
img {
  // تأثير hover وshadow موحد لكل الصور (عدا صور السلايدر)
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  &:hover {
    transform: scale(1.03);
    box-shadow: 0 4px 16px rgba(0,0,0,0.13);
  }
}
// استثناء صور السلايدر من تأثير hover
.intro .swiper .swiper-slide img,
.home-page .swiper-container .swiper-slide img {
  box-shadow: none;
  transition: none;
  &:hover {
    transform: none;
    box-shadow: none;
  }
}
// أنماط الصور الأساسية (aspect, responsive, circular, rectangle, square)
.aspect-img { height: 0; overflow: hidden; position: relative; width: 100%;
  img { height: 100%; left: 0; object-fit: cover; position: absolute; top: 0; transition: transform 0.3s; width: 100%; }
}
.aspect-1-1 { padding-bottom: 100%; } // نسبة 1:1
.aspect-4-3 { padding-bottom: 75%; } // نسبة 4:3
.aspect-16-9 { padding-bottom: 56.25%; } // نسبة 16:9
.aspect-3-4 { padding-bottom: 133.33%; } // نسبة 3:4
.img-responsive { display: block; height: auto; width: 100%; }
.img-circular { border-radius: 50%; display: block; height: 200px; object-fit: cover; width: 200px; }
.img-rectangle { border-radius: 8px; display: block; height: auto; object-fit: cover; width: 100%; }
.img-square { border-radius: 8px; display: block; height: 200px; object-fit: cover; width: 200px; }
// الصور مع إطار أو ظل أو تسمية توضيحية
.img-shadow { box-shadow: 0 2px 8px rgba(0,0,0,0.12); }
.img-border { border: 2px solid $mainColor; padding: 5px; }
.img-caption { margin-bottom: 30px; position: relative;
  img { border-radius: 8px; width: 100%; }
  .caption { background-color: rgba(0,0,0,0.7); bottom: -25px; color: #fff; font-size: 0.9rem; left: 0; padding: 5px 10px; position: absolute; text-align: center; width: 100%; }
} 