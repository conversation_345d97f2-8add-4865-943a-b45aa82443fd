var _g;
function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
import * as React from "react";
function SvgSnapchat(_ref, svgRef) {
  let {
    title,
    titleId,
    ...props
  } = _ref;
  return /*#__PURE__*/React.createElement("svg", _extends({
    id: "Layer_1",
    enableBackground: "new 0 0 176 176",
    height: 512,
    viewBox: "0 0 176 176",
    width: 512,
    xmlns: "http://www.w3.org/2000/svg",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? /*#__PURE__*/React.createElement("title", {
    id: titleId
  }, title) : null, _g || (_g = /*#__PURE__*/React.createElement("g", null, /*#__PURE__*/React.createElement("g", {
    id: "_05.snapchat"
  }, /*#__PURE__*/React.createElement("path", {
    id: "background",
    d: "m144.5 173c-37.6 4-75.4 4-113 0-15-1.6-26.9-13.5-28.5-28.5-4-37.6-4-75.4 0-113 1.6-15 13.5-26.9 28.5-28.5 37.6-4 75.4-4 113 0 15 1.6 26.9 13.5 28.5 28.5 4 37.6 4 75.4 0 113-1.6 15-13.5 26.9-28.5 28.5z",
    fill: "#fffc00"
  }), /*#__PURE__*/React.createElement("path", {
    id: "icon",
    d: "m88.7 139.7h-.8-.6c-6.8 0-11.3-3.2-15.2-5.9-2.6-1.8-5-3.5-7.7-4-1.4-.2-2.7-.3-4.1-.3-1.9 0-3.9.2-5.8.6-.8.2-1.7.3-2.5.4-1.4.1-2.7-.8-3-2.2-.3-.9-.5-1.8-.6-2.6-.2-.8-.4-1.6-.6-2.3-8.6-1.3-13.3-3.3-14.5-6-.2-.4-.2-.8-.3-1.2-.1-1.3.9-2.5 2.2-2.8 14.6-2.4 21.3-17.4 21.5-18.1l.1-.1c.7-1.1.9-2.4.5-3.7-.8-1.9-3.9-2.8-5.9-3.5-.6-.2-1.2-.4-1.6-.6-5.3-2.1-6-4.6-5.7-6.3.5-2.4 3.3-4 5.7-4 .6 0 1.3.1 1.9.4 1.5.8 3.2 1.2 4.9 1.3.6 0 1.2-.1 1.8-.3 0-.6-.2-2.4-.2-2.4-.5-7.5-1-16.8 1.4-22.3 7.3-16.3 22.8-17.6 27.3-17.6h2.2c4.6 0 20.1 1.3 27.4 17.6 2.5 5.5 1.9 14.8 1.4 22.3 0 0-.1 1.7-.2 2.4.5.2 1 .3 1.4.3 1.6-.1 3.1-.6 4.6-1.3.7-.3 1.5-.5 2.3-.5s1.7.2 2.4.5c2.1.8 3.5 2.4 3.5 4.1.1 2.3-1.9 4.2-5.8 5.7-.4.2-.9.3-1.5.5h-.1c-2 .7-5.1 1.6-5.9 3.5-.4 1.2-.2 2.6.5 3.7l.1.2c.3.6 6.9 15.7 21.5 18.1 1.3.2 2.3 1.4 2.2 2.7 0 .4-.1.8-.3 1.2-1.1 2.7-5.9 4.7-14.5 6-.3.8-.5 1.5-.6 2.3-.2.9-.4 1.7-.6 2.6-.3 1.3-1.5 2.2-2.9 2.1-.9 0-1.7-.1-2.5-.3-1.9-.4-3.8-.6-5.8-.6-1.4 0-2.8.1-4.1.3-2.7.4-5.1 2.2-7.7 4-3.9 3-8.4 6.1-15.2 6.1z",
    fill: "#fff"
  })))));
}
const ForwardRef = /*#__PURE__*/React.forwardRef(SvgSnapchat);
export default __webpack_public_path__ + "static/media/snapchat.8410ef1e8013b89f1455f006ea3f7aaf.svg";
export { ForwardRef as ReactComponent };