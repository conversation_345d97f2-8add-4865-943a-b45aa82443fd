/**
 * ملف تجاوب الهواتف المحمولة
 *
 * هذا الملف يحتوي على أنماط تجاوب الهواتف المحمولة (أقل من 768px)
 *
 * الأنماط المعرفة في هذا الملف:
 * - استجابة للشاشات الصغيرة (576px - 767px)
 * - استجابة للشاشات الصغيرة جدًا (أقل من 576px)
 * - استجابة للشاشات الصغيرة جدًا (أقل من 480px)
 * - تعديلات للعناوين في الشاشات الصغيرة
 * - تعديلات للصور في الشاشات الصغيرة
 * - تعديلات للفقرات في الشاشات الصغيرة
 * - تعديلات للقوائم في الشاشات الصغيرة
 * - تعديلات لصفحة Molasses في الشاشات الصغيرة
 * - تعديلات للصور الدائرية في الشاشات الصغيرة
 * - تعديلات للصور التي تطفو حول النص في الشاشات الصغيرة
 * - تعديلات للصور كاملة العرض في الشاشات الصغيرة
 *
 * المتغيرات والدوال المستوردة من ملفات أخرى:
 * - من ملف abstracts/_variables.scss: $mainColor
 * - من ملف abstracts/_breakpoints.scss: $breakpoints
 * - من ملف abstracts/_mixins.scss: respond-to()
 */

// =============================
// تم تبسيط الاستدعاءات: الآن نعتمد فقط على ملف أدوات الملاءمة الموحد
@use './_responsive-helpers.scss' as *;
@use "sass:map";

$breakpoint-sm: map.get($breakpoints, 'sm');
$breakpoint-md: map.get($breakpoints, 'md');

// ======================================
// الشاشات الصغيرة (576px - 767px)
// ======================================
@include media('md') {
  .container {
    max-width: 90%;
  }

  // تعديلات للعناوين
  h1 {
    font-size: 2rem;
  }

  h2 {
    font-size: 1.5rem;
  }

  h3 {
    font-size: 25px;
  }



  // تعديلات للأقسام
  section {
    padding: 40px 0;
  }

  // تعديلات للقوائم
  .content-area {
    ul, ol {
      padding-left: 15px;
    }
  }
}
// ======================================
// تصحيحات عامة للشاشات الصغيرة (أقل من 768px)
// ======================================
@include media('md-down') {
  // تصحيح عرض المحتوى
  .container {
    max-width: 95%;
    overflow-x: hidden;
    padding: 0;
    width: 95%;
  }

  body {
    overflow-x: hidden;
    width: 100%;
  }
}

@include media('md-down') {
  // تصحيح الفقرات
  .paragraphs-wraper .paragraph {
    align-items: center;
    flex-direction: column;
    margin-left: 0;
    margin-right: 0;
    padding: 0;
    width: 100%;
    .img-full-width {
    align-items: center;
    display: flex;
    justify-content: center;
    margin: 20px 0;
    padding: 0;
    width: 100%;
    max-width: 100vw;
  }

  .img-full-width img {
    height: auto;
    max-height: none;
    object-fit: cover;
    width: 100%;
  }
}

  .paragraphs-wraper .paragraph h3 {
    font-size: 25px;
  }

  

  .paragraphs-wraper .paragraph .paragraph-img {
    align-items: center;
    display: flex;
    justify-content: center;
    margin: 0 0 20px;
    max-width: 100vw;
    order: 1;
    width: 93vw;
  }

  .paragraphs-wraper .paragraph .paragraph-img img {
    display: block;
    height: auto;
    margin: 0 auto;
    max-height: none;
    object-fit: cover;
    width: 100%;
  }

  .paragraphs-wraper .paragraph .paragraph-content {
    order: 2;
    padding: 0;
    width: 100%;
  }
}


@include media('md-down') {
  // تعديلات إضافية للقوائم على الشاشات الصغيرة
  .about-page .paragraphs-wraper .paragraph .paragraph-content ul,
  .about-page .paragraphs-wraper .paragraph .paragraph-content ol {
    padding-left: 15px;
  }

  .about-page .paragraphs-wraper .paragraph .paragraph-content ul li,
  .about-page .paragraphs-wraper .paragraph .paragraph-content ol li {
    line-height: 1.5;
    margin-bottom: 10px;
  }

  // تنسيق عام للعناصر span على الشاشات الصغيرة
  .about-page .paragraphs-wraper .paragraph .paragraph-content span {
    color: $mainColor;
    display: block;
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 5px;
  }

  .about-page .paragraphs-wraper .paragraph .paragraph-content ul li span,
  .about-page .paragraphs-wraper .paragraph .paragraph-content ol li span {
    color: $mainColor;
    display: block;
    font-size: 15px;
    font-weight: 700;
    margin-bottom: 3px;
  }
}

// ======================================
// تصحيحات إضافية للشاشات الصغيرة جدًا (أقل من 576px)
// ======================================
@include media('sm') {
  // تعديلات للعناوين
  h1 {
    font-size: 1.75rem;
  }

  h2 {
    font-size: 1.25rem;
  }

  h3 {
    font-size: 28px;
  }


  // تعديلات للأقسام
  section {
    padding: 30px 0;
  }

  // تعديلات للهوامش
  .mb-sm-3 {
    margin-bottom: 1rem;
  }

  .mt-sm-3 {
    margin-top: 1rem;
  }

  // تعديلات للعرض
  .d-sm-none {
    display: none;
  }

  .d-sm-block {
    display: block;
  }

  .d-sm-flex {
    display: flex;
  }

  // تعديلات للنصوص
  .text-sm-center {
    text-align: center;
  }
}



// ======================================
// أمثلة على استخدام المزج الموحد مع الاتجاه والميزات الإضافية
// ======================================

// مثال على استخدام المزج مع الاتجاه الطولي
@include media('md', 'portrait') {
  .portrait-only {
    display: block;
  }

  .landscape-only {
    display: none;
  }
}

// مثال على استخدام المزج مع الاتجاه الأفقي
@include media('md', 'landscape') {
  .portrait-only {
    display: none;
  }

  .landscape-only {
    display: block;
  }
}

// مثال على استخدام المزج مع ميزة إضافية (ارتفاع الشاشة)
@include media('md', null, 'min-height: 600px') {
  .tall-screen {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }
}

// مثال على استخدام المزج مع الاتجاه وميزة إضافية
@include media('sm', 'portrait', 'max-height: 700px') {
  .small-portrait {
    font-size: 0.9rem;
  }
}

// تم نقل تصحيحات الشاشات الطولية إلى ملف themes/_customizations.scss

/*
  =============================
  أنماط التجاوب للموبايل
  منقولة من الهيكل الاحترافي public/styles/responsive/_mobile.scss
  - تشمل: تصحيح العناوين، الصور، الفقرات، القوائم، صفحات molasses، الصور الدائرية، الصور الكاملة
  =============================
*/

@include media('md', 'portrait') {
  .intro {
    .swiper {
      .swiper-slide {
        padding-left: 0;
      }
      .container {
        padding-left: 10px;
        padding-right: 10px;
      }
      .swiper-pagination {
        left: 50% !important;
        transform: translateX(-50%);
        right: unset;
      }
      .slide-img img {
        width: 100%;
        height: auto;
        max-height: 60vh;
        object-fit: contain;
        margin-right: 0 !important;
      }
    }
  }
}
